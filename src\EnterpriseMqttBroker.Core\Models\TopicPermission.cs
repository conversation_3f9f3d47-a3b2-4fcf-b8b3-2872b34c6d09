using System.ComponentModel.DataAnnotations;

namespace EnterpriseMqttBroker.Core.Models;

/// <summary>
/// 主题权限实体模型
/// </summary>
public class TopicPermission
{
    /// <summary>
    /// 权限唯一标识
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 角色ID
    /// </summary>
    public Guid RoleId { get; set; }

    /// <summary>
    /// 主题模式（支持通配符）
    /// </summary>
    [Required]
    [StringLength(255)]
    public string TopicPattern { get; set; } = string.Empty;

    /// <summary>
    /// 是否允许发布
    /// </summary>
    public bool CanPublish { get; set; } = false;

    /// <summary>
    /// 是否允许订阅
    /// </summary>
    public bool CanSubscribe { get; set; } = false;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 角色导航属性
    /// </summary>
    public virtual Role Role { get; set; } = null!;
}
