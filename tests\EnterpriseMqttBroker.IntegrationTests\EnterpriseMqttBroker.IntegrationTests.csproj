<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.12" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.6" />
    <PackageReference Include="Testcontainers" Version="4.1.0" />
    <PackageReference Include="Testcontainers.MsSql" Version="4.1.0" />
    <PackageReference Include="Testcontainers.Redis" Version="4.1.0" />
    <PackageReference Include="FluentAssertions" Version="7.0.0" />
    <PackageReference Include="MQTTnet" Version="5.0.1.1416" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\EnterpriseMqttBroker.Core\EnterpriseMqttBroker.Core.csproj" />
    <ProjectReference Include="..\..\src\EnterpriseMqttBroker.Infrastructure\EnterpriseMqttBroker.Infrastructure.csproj" />
    <ProjectReference Include="..\..\src\EnterpriseMqttBroker.MqttService\EnterpriseMqttBroker.MqttService.csproj" />
    <ProjectReference Include="..\..\src\EnterpriseMqttBroker.WebApi\EnterpriseMqttBroker.WebApi.csproj" />
  </ItemGroup>

</Project>
