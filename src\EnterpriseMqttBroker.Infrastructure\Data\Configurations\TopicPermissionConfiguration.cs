using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EnterpriseMqttBroker.Core.Models;

namespace EnterpriseMqttBroker.Infrastructure.Data.Configurations;

/// <summary>
/// 主题权限实体配置
/// </summary>
public class TopicPermissionConfiguration : IEntityTypeConfiguration<TopicPermission>
{
    /// <summary>
    /// 配置主题权限实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<TopicPermission> builder)
    {
        // 表名
        builder.ToTable("TopicPermissions");

        // 主键
        builder.HasKey(tp => tp.Id);
        builder.Property(tp => tp.Id)
            .HasDefaultValueSql("NEWID()");

        // 角色ID
        builder.Property(tp => tp.RoleId)
            .IsRequired()
            .HasComment("角色ID");

        // 主题模式
        builder.Property(tp => tp.TopicPattern)
            .IsRequired()
            .HasMaxLength(255)
            .HasComment("主题模式（支持通配符）");

        // 发布权限
        builder.Property(tp => tp.CanPublish)
            .IsRequired()
            .HasDefaultValue(false)
            .HasComment("是否允许发布");

        // 订阅权限
        builder.Property(tp => tp.CanSubscribe)
            .IsRequired()
            .HasDefaultValue(false)
            .HasComment("是否允许订阅");

        // 外键关系
        builder.HasOne(tp => tp.Role)
            .WithMany(r => r.TopicPermissions)
            .HasForeignKey(tp => tp.RoleId)
            .OnDelete(DeleteBehavior.Cascade);

        // 索引
        builder.HasIndex(tp => tp.RoleId)
            .HasDatabaseName("IX_TopicPermissions_RoleId");

        builder.HasIndex(tp => tp.TopicPattern)
            .HasDatabaseName("IX_TopicPermissions_TopicPattern");

        // 复合索引用于权限查询优化
        builder.HasIndex(tp => new { tp.RoleId, tp.TopicPattern })
            .HasDatabaseName("IX_TopicPermissions_RoleId_TopicPattern");

        builder.HasIndex(tp => new { tp.TopicPattern, tp.CanPublish })
            .HasFilter("[CanPublish] = 1")
            .HasDatabaseName("IX_TopicPermissions_TopicPattern_CanPublish");

        builder.HasIndex(tp => new { tp.TopicPattern, tp.CanSubscribe })
            .HasFilter("[CanSubscribe] = 1")
            .HasDatabaseName("IX_TopicPermissions_TopicPattern_CanSubscribe");
    }
}
