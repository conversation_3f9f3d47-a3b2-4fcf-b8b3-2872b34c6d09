﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace EnterpriseMqttBroker.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Mqtt_PersistedMessages",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false, comment: "消息ID")
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ClientId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "客户端ID"),
                    Topic = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false, comment: "消息主题"),
                    Payload = table.Column<byte[]>(type: "varbinary(max)", nullable: true, comment: "消息载荷"),
                    QoSLevel = table.Column<byte>(type: "tinyint", nullable: false, comment: "QoS级别"),
                    Retain = table.Column<bool>(type: "bit", nullable: false, defaultValue: false, comment: "是否为保留消息"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "创建时间"),
                    ExpiryInterval = table.Column<int>(type: "int", nullable: true, comment: "消息过期间隔（秒）")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Mqtt_PersistedMessages", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Mqtt_Roles",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWID()"),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "角色名称"),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "角色描述"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Mqtt_Roles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Mqtt_Users",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWID()"),
                    Username = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "用户名"),
                    PasswordHash = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false, comment: "密码哈希"),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true, comment: "邮箱地址"),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true, comment: "是否激活"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "创建时间"),
                    LastLoginAt = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "最后登录时间"),
                    FailedLoginAttempts = table.Column<int>(type: "int", nullable: false, defaultValue: 0, comment: "失败登录尝试次数"),
                    LockoutEndTime = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "锁定结束时间")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Mqtt_Users", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Mqtt_TopicPermissions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWID()"),
                    RoleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "角色ID"),
                    TopicPattern = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false, comment: "主题模式（支持通配符）"),
                    CanPublish = table.Column<bool>(type: "bit", nullable: false, defaultValue: false, comment: "是否允许发布"),
                    CanSubscribe = table.Column<bool>(type: "bit", nullable: false, defaultValue: false, comment: "是否允许订阅"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Mqtt_TopicPermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Mqtt_TopicPermissions_Mqtt_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Mqtt_Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Mqtt_UserRoles",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "用户ID"),
                    RoleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, comment: "角色ID"),
                    AssignedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Mqtt_UserRoles", x => new { x.UserId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_Mqtt_UserRoles_Mqtt_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Mqtt_Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Mqtt_UserRoles_Mqtt_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Mqtt_Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "Mqtt_Roles",
                columns: new[] { "Id", "CreatedAt", "Description", "Name" },
                values: new object[,]
                {
                    { new Guid("12e30b31-c8d5-4907-8833-dd531dd649ec"), new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc), "系统管理员，拥有所有权限", "Administrator" },
                    { new Guid("2363d76a-2723-4a1b-8d96-a994725b905c"), new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc), "普通用户，拥有基本MQTT权限", "User" }
                });

            migrationBuilder.InsertData(
                table: "Mqtt_Users",
                columns: new[] { "Id", "CreatedAt", "Email", "IsActive", "LastLoginAt", "LockoutEndTime", "PasswordHash", "Username" },
                values: new object[] { new Guid("f676f4ff-0509-46e3-8f4f-c2ff4599d21f"), new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc), "<EMAIL>", true, null, null, "$2a$11$XwCUWaFSOh6dEHKonXZ1dezlNFB90BiK.K5PIfISTl/l7dZn3cNgq", "admin" });

            migrationBuilder.InsertData(
                table: "Mqtt_TopicPermissions",
                columns: new[] { "Id", "CanPublish", "CanSubscribe", "CreatedAt", "RoleId", "TopicPattern" },
                values: new object[] { new Guid("b96a77f5-1674-4199-bb73-361de6d1ccac"), true, true, new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc), new Guid("2363d76a-2723-4a1b-8d96-a994725b905c"), "user/+/data" });

            migrationBuilder.InsertData(
                table: "Mqtt_TopicPermissions",
                columns: new[] { "Id", "CanSubscribe", "CreatedAt", "RoleId", "TopicPattern" },
                values: new object[] { new Guid("c7969c1b-7072-431a-bbcb-2dc6111860ac"), true, new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc), new Guid("2363d76a-2723-4a1b-8d96-a994725b905c"), "public/#" });

            migrationBuilder.InsertData(
                table: "Mqtt_TopicPermissions",
                columns: new[] { "Id", "CanPublish", "CanSubscribe", "CreatedAt", "RoleId", "TopicPattern" },
                values: new object[] { new Guid("f3611cb9-f0f5-4f93-8b89-8a9ed104658c"), true, true, new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc), new Guid("12e30b31-c8d5-4907-8833-dd531dd649ec"), "#" });

            migrationBuilder.InsertData(
                table: "Mqtt_UserRoles",
                columns: new[] { "RoleId", "UserId", "AssignedAt" },
                values: new object[] { new Guid("12e30b31-c8d5-4907-8833-dd531dd649ec"), new Guid("f676f4ff-0509-46e3-8f4f-c2ff4599d21f"), new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc) });

            migrationBuilder.CreateIndex(
                name: "IX_PersistedMessages_ClientId",
                table: "Mqtt_PersistedMessages",
                column: "ClientId");

            migrationBuilder.CreateIndex(
                name: "IX_PersistedMessages_ClientId_CreatedAt",
                table: "Mqtt_PersistedMessages",
                columns: new[] { "ClientId", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_PersistedMessages_CreatedAt",
                table: "Mqtt_PersistedMessages",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_PersistedMessages_ExpiryInterval_CreatedAt",
                table: "Mqtt_PersistedMessages",
                columns: new[] { "ExpiryInterval", "CreatedAt" },
                filter: "[ExpiryInterval] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_PersistedMessages_QoSLevel",
                table: "Mqtt_PersistedMessages",
                column: "QoSLevel");

            migrationBuilder.CreateIndex(
                name: "IX_PersistedMessages_QoSLevel_CreatedAt",
                table: "Mqtt_PersistedMessages",
                columns: new[] { "QoSLevel", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_PersistedMessages_Topic",
                table: "Mqtt_PersistedMessages",
                column: "Topic");

            migrationBuilder.CreateIndex(
                name: "IX_PersistedMessages_Topic_CreatedAt",
                table: "Mqtt_PersistedMessages",
                columns: new[] { "Topic", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_PersistedMessages_Topic_Retain",
                table: "Mqtt_PersistedMessages",
                columns: new[] { "Topic", "Retain" },
                filter: "[Retain] = 1");

            migrationBuilder.CreateIndex(
                name: "IX_Roles_Name",
                table: "Mqtt_Roles",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TopicPermissions_RoleId",
                table: "Mqtt_TopicPermissions",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_TopicPermissions_RoleId_TopicPattern",
                table: "Mqtt_TopicPermissions",
                columns: new[] { "RoleId", "TopicPattern" });

            migrationBuilder.CreateIndex(
                name: "IX_TopicPermissions_TopicPattern",
                table: "Mqtt_TopicPermissions",
                column: "TopicPattern");

            migrationBuilder.CreateIndex(
                name: "IX_TopicPermissions_TopicPattern_CanPublish",
                table: "Mqtt_TopicPermissions",
                columns: new[] { "TopicPattern", "CanPublish" },
                filter: "[CanPublish] = 1");

            migrationBuilder.CreateIndex(
                name: "IX_TopicPermissions_TopicPattern_CanSubscribe",
                table: "Mqtt_TopicPermissions",
                columns: new[] { "TopicPattern", "CanSubscribe" },
                filter: "[CanSubscribe] = 1");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_RoleId",
                table: "Mqtt_UserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "IX_UserRoles_UserId",
                table: "Mqtt_UserRoles",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_CreatedAt",
                table: "Mqtt_Users",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Email",
                table: "Mqtt_Users",
                column: "Email",
                unique: true,
                filter: "[Email] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Users_IsActive",
                table: "Mqtt_Users",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Users_LastLoginAt",
                table: "Mqtt_Users",
                column: "LastLoginAt");

            migrationBuilder.CreateIndex(
                name: "IX_Users_LockoutEndTime",
                table: "Mqtt_Users",
                column: "LockoutEndTime",
                filter: "[LockoutEndTime] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Users_Username",
                table: "Mqtt_Users",
                column: "Username",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Mqtt_PersistedMessages");

            migrationBuilder.DropTable(
                name: "Mqtt_TopicPermissions");

            migrationBuilder.DropTable(
                name: "Mqtt_UserRoles");

            migrationBuilder.DropTable(
                name: "Mqtt_Roles");

            migrationBuilder.DropTable(
                name: "Mqtt_Users");
        }
    }
}
