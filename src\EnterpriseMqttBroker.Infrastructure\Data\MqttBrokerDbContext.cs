using Microsoft.EntityFrameworkCore;
using EnterpriseMqttBroker.Core.Models;
using EnterpriseMqttBroker.Infrastructure.Data.Configurations;

namespace EnterpriseMqttBroker.Infrastructure.Data;

/// <summary>
/// MQTT Broker 数据库上下文
/// </summary>
public class MqttBrokerDbContext : DbContext
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="options">数据库上下文选项</param>
    public MqttBrokerDbContext(DbContextOptions<MqttBrokerDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// 用户表
    /// </summary>
    public DbSet<User> Users { get; set; } = null!;

    /// <summary>
    /// 角色表
    /// </summary>
    public DbSet<Role> Roles { get; set; } = null!;

    /// <summary>
    /// 用户角色关联表
    /// </summary>
    public DbSet<UserRole> UserRoles { get; set; } = null!;

    /// <summary>
    /// 主题权限表
    /// </summary>
    public DbSet<TopicPermission> TopicPermissions { get; set; } = null!;

    /// <summary>
    /// 持久化消息表
    /// </summary>
    public DbSet<PersistedMessage> PersistedMessages { get; set; } = null!;

    /// <summary>
    /// 配置实体模型
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 应用实体配置
        modelBuilder.ApplyConfiguration(new UserConfiguration());
        modelBuilder.ApplyConfiguration(new RoleConfiguration());
        modelBuilder.ApplyConfiguration(new UserRoleConfiguration());
        modelBuilder.ApplyConfiguration(new TopicPermissionConfiguration());
        modelBuilder.ApplyConfiguration(new PersistedMessageConfiguration());

        // 设置表名前缀
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            var tableName = entityType.GetTableName();
            if (!string.IsNullOrEmpty(tableName))
            {
                entityType.SetTableName($"Mqtt_{tableName}");
            }
        }

        // 配置索引
        ConfigureIndexes(modelBuilder);

        // 种子数据
        SeedData(modelBuilder);
    }

    /// <summary>
    /// 配置索引
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    private static void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // 用户表索引
        modelBuilder.Entity<User>()
            .HasIndex(u => u.Username)
            .IsUnique()
            .HasDatabaseName("IX_Users_Username");

        modelBuilder.Entity<User>()
            .HasIndex(u => u.Email)
            .IsUnique()
            .HasFilter("[Email] IS NOT NULL")
            .HasDatabaseName("IX_Users_Email");

        modelBuilder.Entity<User>()
            .HasIndex(u => u.IsActive)
            .HasDatabaseName("IX_Users_IsActive");

        modelBuilder.Entity<User>()
            .HasIndex(u => u.CreatedAt)
            .HasDatabaseName("IX_Users_CreatedAt");

        // 角色表索引
        modelBuilder.Entity<Role>()
            .HasIndex(r => r.Name)
            .IsUnique()
            .HasDatabaseName("IX_Roles_Name");

        // 主题权限表索引
        modelBuilder.Entity<TopicPermission>()
            .HasIndex(tp => tp.TopicPattern)
            .HasDatabaseName("IX_TopicPermissions_TopicPattern");

        modelBuilder.Entity<TopicPermission>()
            .HasIndex(tp => tp.RoleId)
            .HasDatabaseName("IX_TopicPermissions_RoleId");

        // 持久化消息表索引
        modelBuilder.Entity<PersistedMessage>()
            .HasIndex(pm => pm.ClientId)
            .HasDatabaseName("IX_PersistedMessages_ClientId");

        modelBuilder.Entity<PersistedMessage>()
            .HasIndex(pm => pm.Topic)
            .HasDatabaseName("IX_PersistedMessages_Topic");

        modelBuilder.Entity<PersistedMessage>()
            .HasIndex(pm => pm.CreatedAt)
            .HasDatabaseName("IX_PersistedMessages_CreatedAt");

        modelBuilder.Entity<PersistedMessage>()
            .HasIndex(pm => new { pm.Topic, pm.Retain })
            .HasFilter("[Retain] = 1")
            .HasDatabaseName("IX_PersistedMessages_Topic_Retain");

        modelBuilder.Entity<PersistedMessage>()
            .HasIndex(pm => pm.QoSLevel)
            .HasDatabaseName("IX_PersistedMessages_QoSLevel");
    }

    /// <summary>
    /// 种子数据
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    private static void SeedData(ModelBuilder modelBuilder)
    {
        // 使用固定的GUID和时间戳，避免动态值导致的模型变化
        var adminRoleId = new Guid("12e30b31-c8d5-4907-8833-dd531dd649ec");
        var userRoleId = new Guid("2363d76a-2723-4a1b-8d96-a994725b905c");
        var adminUserId = new Guid("f676f4ff-0509-46e3-8f4f-c2ff4599d21f");
        var seedDateTime = new DateTime(2025, 1, 15, 0, 0, 0, DateTimeKind.Utc);

        modelBuilder.Entity<Role>().HasData(
            new Role
            {
                Id = adminRoleId,
                Name = "Administrator",
                Description = "系统管理员，拥有所有权限",
                CreatedAt = seedDateTime
            },
            new Role
            {
                Id = userRoleId,
                Name = "User",
                Description = "普通用户，拥有基本MQTT权限",
                CreatedAt = seedDateTime
            }
        );

        // 默认管理员用户
        modelBuilder.Entity<User>().HasData(
            new User
            {
                Id = adminUserId,
                Username = "admin",
                PasswordHash = "$2a$11$XwCUWaFSOh6dEHKonXZ1dezlNFB90BiK.K5PIfISTl/l7dZn3cNgq", // admin123的BCrypt哈希
                Email = "<EMAIL>",
                IsActive = true,
                CreatedAt = seedDateTime,
                FailedLoginAttempts = 0
            }
        );

        // 管理员角色分配
        modelBuilder.Entity<UserRole>().HasData(
            new UserRole
            {
                UserId = adminUserId,
                RoleId = adminRoleId,
                AssignedAt = seedDateTime
            }
        );

        // 默认主题权限
        modelBuilder.Entity<TopicPermission>().HasData(
            new TopicPermission
            {
                Id = new Guid("f3611cb9-f0f5-4f93-8b89-8a9ed104658c"),
                RoleId = adminRoleId,
                TopicPattern = "#",
                CanPublish = true,
                CanSubscribe = true,
                CreatedAt = seedDateTime
            },
            new TopicPermission
            {
                Id = new Guid("b96a77f5-1674-4199-bb73-361de6d1ccac"),
                RoleId = userRoleId,
                TopicPattern = "user/+/data",
                CanPublish = true,
                CanSubscribe = true,
                CreatedAt = seedDateTime
            },
            new TopicPermission
            {
                Id = new Guid("c7969c1b-7072-431a-bbcb-2dc6111860ac"),
                RoleId = userRoleId,
                TopicPattern = "public/#",
                CanPublish = false,
                CanSubscribe = true,
                CreatedAt = seedDateTime
            }
        );
    }

    /// <summary>
    /// 保存更改前的处理
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // 自动设置审计字段
        var entries = ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            if (entry.Entity is User user)
            {
                if (entry.State == EntityState.Added)
                {
                    user.CreatedAt = DateTime.UtcNow;
                }
            }
            else if (entry.Entity is PersistedMessage message)
            {
                if (entry.State == EntityState.Added)
                {
                    message.CreatedAt = DateTime.UtcNow;
                }
            }
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}
