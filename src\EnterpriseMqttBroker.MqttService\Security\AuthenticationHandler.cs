using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Caching.Distributed;
using MQTTnet.Server;
using MQTTnet.Protocol;
using EnterpriseMqttBroker.Core.Interfaces;
using EnterpriseMqttBroker.MqttService.Configuration;
using EnterpriseMqttBroker.MqttService.Interfaces;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json;

namespace EnterpriseMqttBroker.MqttService.Security;

/// <summary>
/// MQTT认证处理器实现
/// </summary>
public class AuthenticationHandler : IAuthenticationHandler
{
    private readonly ILogger<AuthenticationHandler> _logger;
    private readonly MqttServerOptions _options;
    private readonly IUserService _userService;
    private readonly IDistributedCache _cache;

    // 认证缓存键前缀
    private const string AUTH_CACHE_PREFIX = "auth:";
    private const string LOCKOUT_CACHE_PREFIX = "lockout:";
    private const string ROLES_CACHE_PREFIX = "roles:";

    public AuthenticationHandler(
        ILogger<AuthenticationHandler> logger,
        IOptions<MqttServerOptions> options,
        IUserService userService,
        IDistributedCache cache)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _userService = userService ?? throw new ArgumentNullException(nameof(userService));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
    }

    public async Task<AuthenticationResult> ValidateConnectionAsync(MqttConnectionValidatorContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查是否启用认证
            if (!_options.EnableAuthentication)
            {
                return AuthenticationResult.Success(context.Username, null);
            }

            // 检查客户端ID
            if (!await IsValidClientIdAsync(context.ClientId, cancellationToken))
            {
                return AuthenticationResult.Failure("无效的客户端ID");
            }

            // 检查锁定状态
            var lockoutInfo = await CheckLockoutAsync(context.Username, context.ClientId, context.Endpoint ?? "Unknown", cancellationToken);
            if (lockoutInfo.IsLockedOut)
            {
                return AuthenticationResult.Failure($"账户已锁定，剩余时间: {lockoutInfo.RemainingLockoutTime?.TotalMinutes:F0} 分钟");
            }

            // 验证凭据
            return await ValidateCredentialsAsync(context.Username, context.Password, context.ClientId, context.Endpoint ?? "Unknown", cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接验证失败: {ClientId}", context.ClientId);
            return AuthenticationResult.Failure("认证服务异常");
        }
    }

    public async Task<AuthenticationResult> ValidateCredentialsAsync(string? username, string? password, string clientId, string endpoint, CancellationToken cancellationToken = default)
    {
        try
        {
            // 允许匿名连接
            if (_options.Security.AllowAnonymousConnections && string.IsNullOrEmpty(username))
            {
                return AuthenticationResult.Success(null, null);
            }

            // 验证用户名和密码
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                return AuthenticationResult.Failure("用户名或密码不能为空");
            }

            // 调用用户服务验证
            var user = await _userService.ValidateUserAsync(username, password, cancellationToken);
            if (user == null)
            {
                return AuthenticationResult.Failure("用户名或密码错误");
            }

            // 获取用户角色
            var roles = await GetUserRolesAsync(username, cancellationToken);

            return AuthenticationResult.Success(username, user.Id.ToString(), roles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "凭据验证失败: {Username}", username);
            return AuthenticationResult.Failure("认证服务异常");
        }
    }

    public async Task<AuthenticationResult> ValidateCertificateAsync(byte[]? certificate, string clientId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (certificate == null || certificate.Length == 0)
            {
                return AuthenticationResult.Failure("证书数据为空");
            }

            // 检查缓存
            var cacheKey = $"{AUTH_CACHE_PREFIX}cert:{ComputeHash(certificate)}";
            var cachedResult = await GetCachedAuthResultAsync(cacheKey, cancellationToken);
            if (cachedResult != null)
            {
                _logger.LogDebug("使用缓存的证书认证结果: {ClientId}", clientId);
                return cachedResult;
            }

            // 解析X.509证书
            var x509Certificate = new X509Certificate2(certificate);

            // 验证证书有效性
            var validationResult = await ValidateX509CertificateAsync(x509Certificate, clientId, cancellationToken);

            // 缓存结果
            await CacheAuthResultAsync(cacheKey, validationResult, TimeSpan.FromMinutes(30), cancellationToken);

            return validationResult;
        }
        catch (CryptographicException ex)
        {
            _logger.LogError(ex, "证书格式无效: {ClientId}", clientId);
            return AuthenticationResult.Failure("证书格式无效");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "证书验证失败: {ClientId}", clientId);
            return AuthenticationResult.Failure("证书验证异常");
        }
    }

    /// <summary>
    /// 验证X.509证书
    /// </summary>
    private async Task<AuthenticationResult> ValidateX509CertificateAsync(X509Certificate2 certificate, string clientId, CancellationToken cancellationToken)
    {
        try
        {
            // 检查证书有效期
            var now = DateTime.UtcNow;
            if (certificate.NotBefore > now || certificate.NotAfter < now)
            {
                return AuthenticationResult.Failure($"证书已过期或尚未生效: {certificate.NotBefore} - {certificate.NotAfter}");
            }

            // 检查证书用途
            var keyUsage = certificate.Extensions.OfType<X509KeyUsageExtension>().FirstOrDefault();
            if (keyUsage != null && !keyUsage.KeyUsages.HasFlag(X509KeyUsageFlags.DigitalSignature))
            {
                return AuthenticationResult.Failure("证书不支持数字签名");
            }

            // 提取客户端标识（从证书主题或SAN扩展）
            var subjectName = certificate.Subject;
            var commonName = ExtractCommonName(subjectName);

            // 验证客户端ID匹配
            if (!string.IsNullOrEmpty(commonName) && commonName != clientId)
            {
                _logger.LogWarning("证书CN与客户端ID不匹配: CN={CommonName}, ClientId={ClientId}", commonName, clientId);
            }

            // 验证证书链（如果配置了CA证书）
            if (!string.IsNullOrEmpty(_options.CertificatePath))
            {
                var chainValidationResult = await ValidateCertificateChainAsync(certificate, cancellationToken);
                if (!chainValidationResult.IsSuccess)
                {
                    return chainValidationResult;
                }
            }

            // 获取证书相关的用户角色
            var roles = await GetCertificateRolesAsync(certificate, cancellationToken);

            return AuthenticationResult.Success(commonName ?? clientId, certificate.Thumbprint, roles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "X.509证书验证失败: {ClientId}", clientId);
            return AuthenticationResult.Failure("证书验证异常");
        }
    }

    /// <summary>
    /// 验证证书链
    /// </summary>
    private async Task<AuthenticationResult> ValidateCertificateChainAsync(X509Certificate2 certificate, CancellationToken cancellationToken)
    {
        try
        {
            using var chain = new X509Chain();

            // 配置链验证选项
            chain.ChainPolicy.RevocationMode = X509RevocationMode.NoCheck; // 在生产环境中应启用
            chain.ChainPolicy.VerificationFlags = X509VerificationFlags.IgnoreWrongUsage;

            // 执行链验证
            var isValid = chain.Build(certificate);

            if (!isValid)
            {
                var errors = string.Join(", ", chain.ChainStatus.Select(s => s.StatusInformation));
                return AuthenticationResult.Failure($"证书链验证失败: {errors}");
            }

            return AuthenticationResult.Success(null, null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "证书链验证异常");
            return AuthenticationResult.Failure("证书链验证异常");
        }
    }

    /// <summary>
    /// 提取证书CN
    /// </summary>
    private string? ExtractCommonName(string subjectName)
    {
        try
        {
            var parts = subjectName.Split(',');
            var cnPart = parts.FirstOrDefault(p => p.Trim().StartsWith("CN=", StringComparison.OrdinalIgnoreCase));
            return cnPart?.Substring(3).Trim();
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 获取证书相关角色
    /// </summary>
    private async Task<IEnumerable<string>> GetCertificateRolesAsync(X509Certificate2 certificate, CancellationToken cancellationToken)
    {
        try
        {
            // 基于证书属性分配默认角色
            var roles = new List<string> { "CertificateUser" };

            // 可以根据证书的组织单位、颁发者等信息分配不同角色
            var organizationUnit = ExtractOrganizationUnit(certificate.Subject);
            if (!string.IsNullOrEmpty(organizationUnit))
            {
                roles.Add($"OU_{organizationUnit}");
            }

            return roles;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取证书角色失败");
            return new[] { "CertificateUser" };
        }
    }

    /// <summary>
    /// 提取组织单位
    /// </summary>
    private string? ExtractOrganizationUnit(string subjectName)
    {
        try
        {
            var parts = subjectName.Split(',');
            var ouPart = parts.FirstOrDefault(p => p.Trim().StartsWith("OU=", StringComparison.OrdinalIgnoreCase));
            return ouPart?.Substring(3).Trim();
        }
        catch
        {
            return null;
        }
    }

    public async Task<bool> IsValidClientIdAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
        {
            return _options.Security.ClientIdValidation == ClientIdValidationMode.None;
        }

        // 检查长度限制
        if (clientId.Length > _options.Security.MaxClientIdLength)
        {
            return false;
        }

        // 根据验证模式检查
        switch (_options.Security.ClientIdValidation)
        {
            case ClientIdValidationMode.None:
                return true;
            case ClientIdValidationMode.Required:
                return !string.IsNullOrEmpty(clientId);
            case ClientIdValidationMode.Unique:
                // TODO: 检查客户端ID唯一性
                return true;
            case ClientIdValidationMode.Custom:
                // TODO: 自定义验证逻辑
                return true;
            default:
                return true;
        }
    }

    public async Task<LockoutInfo> CheckLockoutAsync(string? username, string clientId, string endpoint, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(username))
            {
                return new LockoutInfo { IsLockedOut = false };
            }

            var user = await _userService.GetUserByUsernameAsync(username, cancellationToken);
            if (user == null)
            {
                return new LockoutInfo { IsLockedOut = false };
            }

            // 检查锁定状态
            if (user.LockoutEndTime.HasValue && user.LockoutEndTime.Value > DateTime.UtcNow)
            {
                return new LockoutInfo
                {
                    IsLockedOut = true,
                    LockoutEndTime = user.LockoutEndTime,
                    FailedAttempts = user.FailedLoginAttempts,
                    Reason = "登录失败次数过多"
                };
            }

            return new LockoutInfo
            {
                IsLockedOut = false,
                FailedAttempts = user.FailedLoginAttempts
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查锁定状态失败: {Username}", username);
            return new LockoutInfo { IsLockedOut = false };
        }
    }

    public async Task RecordLoginFailureAsync(string? username, string clientId, string endpoint, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(username))
                return;

            await _userService.RecordLoginFailureAsync(username, cancellationToken);
            _logger.LogWarning("登录失败记录: 用户={Username}, 客户端={ClientId}, 终结点={Endpoint}, 原因={Reason}", 
                username, clientId, endpoint, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录登录失败异常: {Username}", username);
        }
    }

    public async Task RecordLoginSuccessAsync(string? username, string clientId, string endpoint, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(username))
                return;

            await _userService.RecordLoginSuccessAsync(username, cancellationToken);
            _logger.LogInformation("登录成功记录: 用户={Username}, 客户端={ClientId}, 终结点={Endpoint}", 
                username, clientId, endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录登录成功异常: {Username}", username);
        }
    }

    public async Task<IEnumerable<string>> GetUserRolesAsync(string username, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查缓存
            var cacheKey = $"{ROLES_CACHE_PREFIX}{username}";
            var cachedRoles = await GetCachedRolesAsync(cacheKey, cancellationToken);
            if (cachedRoles != null)
            {
                return cachedRoles;
            }

            var roles = await _userService.GetUserRolesAsync(username, cancellationToken);
            var roleNames = roles.Select(r => r.Name).ToList();

            // 缓存角色信息
            await CacheRolesAsync(cacheKey, roleNames, TimeSpan.FromMinutes(15), cancellationToken);

            return roleNames;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户角色失败: {Username}", username);
            return Enumerable.Empty<string>();
        }
    }

    /// <summary>
    /// 计算数据哈希
    /// </summary>
    private string ComputeHash(byte[] data)
    {
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(data);
        return Convert.ToBase64String(hash);
    }

    /// <summary>
    /// 获取缓存的认证结果
    /// </summary>
    private async Task<AuthenticationResult?> GetCachedAuthResultAsync(string cacheKey, CancellationToken cancellationToken)
    {
        try
        {
            var cached = await _cache.GetStringAsync(cacheKey, cancellationToken);
            if (!string.IsNullOrEmpty(cached))
            {
                return JsonSerializer.Deserialize<AuthenticationResult>(cached);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存认证结果失败: {CacheKey}", cacheKey);
        }
        return null;
    }

    /// <summary>
    /// 缓存认证结果
    /// </summary>
    private async Task CacheAuthResultAsync(string cacheKey, AuthenticationResult result, TimeSpan expiration, CancellationToken cancellationToken)
    {
        try
        {
            var json = JsonSerializer.Serialize(result);
            var options = new DistributedCacheEntryOptions
            {
                SlidingExpiration = expiration
            };
            await _cache.SetStringAsync(cacheKey, json, options, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "缓存认证结果失败: {CacheKey}", cacheKey);
        }
    }

    /// <summary>
    /// 获取缓存的角色信息
    /// </summary>
    private async Task<IEnumerable<string>?> GetCachedRolesAsync(string cacheKey, CancellationToken cancellationToken)
    {
        try
        {
            var cached = await _cache.GetStringAsync(cacheKey, cancellationToken);
            if (!string.IsNullOrEmpty(cached))
            {
                return JsonSerializer.Deserialize<List<string>>(cached);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取缓存角色失败: {CacheKey}", cacheKey);
        }
        return null;
    }

    /// <summary>
    /// 缓存角色信息
    /// </summary>
    private async Task CacheRolesAsync(string cacheKey, IEnumerable<string> roles, TimeSpan expiration, CancellationToken cancellationToken)
    {
        try
        {
            var json = JsonSerializer.Serialize(roles);
            var options = new DistributedCacheEntryOptions
            {
                SlidingExpiration = expiration
            };
            await _cache.SetStringAsync(cacheKey, json, options, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "缓存角色失败: {CacheKey}", cacheKey);
        }
    }
}
