using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MQTTnet.Server;
using EnterpriseMqttBroker.Core.Interfaces;
using EnterpriseMqttBroker.MqttService.Configuration;
using EnterpriseMqttBroker.MqttService.Interfaces;

namespace EnterpriseMqttBroker.MqttService.Security;

/// <summary>
/// MQTT认证处理器实现
/// </summary>
public class AuthenticationHandler : IAuthenticationHandler
{
    private readonly ILogger<AuthenticationHandler> _logger;
    private readonly MqttServerOptions _options;
    private readonly IUserService _userService;

    public AuthenticationHandler(
        ILogger<AuthenticationHandler> logger,
        IOptions<MqttServerOptions> options,
        IUserService userService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _userService = userService ?? throw new ArgumentNullException(nameof(userService));
    }

    public async Task<AuthenticationResult> ValidateConnectionAsync(MqttConnectionValidatorContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查是否启用认证
            if (!_options.EnableAuthentication)
            {
                return AuthenticationResult.Success(context.Username, null);
            }

            // 检查客户端ID
            if (!await IsValidClientIdAsync(context.ClientId, cancellationToken))
            {
                return AuthenticationResult.Failure("无效的客户端ID");
            }

            // 检查锁定状态
            var lockoutInfo = await CheckLockoutAsync(context.Username, context.ClientId, context.Endpoint ?? "Unknown", cancellationToken);
            if (lockoutInfo.IsLockedOut)
            {
                return AuthenticationResult.Failure($"账户已锁定，剩余时间: {lockoutInfo.RemainingLockoutTime?.TotalMinutes:F0} 分钟");
            }

            // 验证凭据
            return await ValidateCredentialsAsync(context.Username, context.Password, context.ClientId, context.Endpoint ?? "Unknown", cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接验证失败: {ClientId}", context.ClientId);
            return AuthenticationResult.Failure("认证服务异常");
        }
    }

    public async Task<AuthenticationResult> ValidateCredentialsAsync(string? username, string? password, string clientId, string endpoint, CancellationToken cancellationToken = default)
    {
        try
        {
            // 允许匿名连接
            if (_options.Security.AllowAnonymousConnections && string.IsNullOrEmpty(username))
            {
                return AuthenticationResult.Success(null, null);
            }

            // 验证用户名和密码
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                return AuthenticationResult.Failure("用户名或密码不能为空");
            }

            // 调用用户服务验证
            var user = await _userService.ValidateUserAsync(username, password, cancellationToken);
            if (user == null)
            {
                return AuthenticationResult.Failure("用户名或密码错误");
            }

            // 获取用户角色
            var roles = await GetUserRolesAsync(username, cancellationToken);

            return AuthenticationResult.Success(username, user.Id.ToString(), roles);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "凭据验证失败: {Username}", username);
            return AuthenticationResult.Failure("认证服务异常");
        }
    }

    public async Task<AuthenticationResult> ValidateCertificateAsync(byte[]? certificate, string clientId, CancellationToken cancellationToken = default)
    {
        // TODO: 实现证书验证
        await Task.CompletedTask;
        return AuthenticationResult.Failure("证书认证暂未实现");
    }

    public async Task<bool> IsValidClientIdAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
        {
            return _options.Security.ClientIdValidation == ClientIdValidationMode.None;
        }

        // 检查长度限制
        if (clientId.Length > _options.Security.MaxClientIdLength)
        {
            return false;
        }

        // 根据验证模式检查
        switch (_options.Security.ClientIdValidation)
        {
            case ClientIdValidationMode.None:
                return true;
            case ClientIdValidationMode.Required:
                return !string.IsNullOrEmpty(clientId);
            case ClientIdValidationMode.Unique:
                // TODO: 检查客户端ID唯一性
                return true;
            case ClientIdValidationMode.Custom:
                // TODO: 自定义验证逻辑
                return true;
            default:
                return true;
        }
    }

    public async Task<LockoutInfo> CheckLockoutAsync(string? username, string clientId, string endpoint, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(username))
            {
                return new LockoutInfo { IsLockedOut = false };
            }

            var user = await _userService.GetUserByUsernameAsync(username, cancellationToken);
            if (user == null)
            {
                return new LockoutInfo { IsLockedOut = false };
            }

            // 检查锁定状态
            if (user.LockoutEndTime.HasValue && user.LockoutEndTime.Value > DateTime.UtcNow)
            {
                return new LockoutInfo
                {
                    IsLockedOut = true,
                    LockoutEndTime = user.LockoutEndTime,
                    FailedAttempts = user.FailedLoginAttempts,
                    Reason = "登录失败次数过多"
                };
            }

            return new LockoutInfo
            {
                IsLockedOut = false,
                FailedAttempts = user.FailedLoginAttempts
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查锁定状态失败: {Username}", username);
            return new LockoutInfo { IsLockedOut = false };
        }
    }

    public async Task RecordLoginFailureAsync(string? username, string clientId, string endpoint, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(username))
                return;

            await _userService.RecordLoginFailureAsync(username, cancellationToken);
            _logger.LogWarning("登录失败记录: 用户={Username}, 客户端={ClientId}, 终结点={Endpoint}, 原因={Reason}", 
                username, clientId, endpoint, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录登录失败异常: {Username}", username);
        }
    }

    public async Task RecordLoginSuccessAsync(string? username, string clientId, string endpoint, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(username))
                return;

            await _userService.RecordLoginSuccessAsync(username, cancellationToken);
            _logger.LogInformation("登录成功记录: 用户={Username}, 客户端={ClientId}, 终结点={Endpoint}", 
                username, clientId, endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录登录成功异常: {Username}", username);
        }
    }

    public async Task<IEnumerable<string>> GetUserRolesAsync(string username, CancellationToken cancellationToken = default)
    {
        try
        {
            var roles = await _userService.GetUserRolesAsync(username, cancellationToken);
            return roles.Select(r => r.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户角色失败: {Username}", username);
            return Enumerable.Empty<string>();
        }
    }
}
