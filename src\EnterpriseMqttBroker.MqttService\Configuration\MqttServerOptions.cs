namespace EnterpriseMqttBroker.MqttService.Configuration;

/// <summary>
/// MQTT服务器配置选项
/// </summary>
public class MqttServerOptions
{
    /// <summary>
    /// 配置节名称
    /// </summary>
    public const string SectionName = "MqttServer";

    /// <summary>
    /// TCP监听端口
    /// </summary>
    public int TcpPort { get; set; } = 1883;

    /// <summary>
    /// 是否启用WebSocket支持
    /// </summary>
    public bool EnableWebSocket { get; set; } = true;

    /// <summary>
    /// WebSocket监听端口
    /// </summary>
    public int WebSocketPort { get; set; } = 8083;

    /// <summary>
    /// 是否启用SSL/TLS
    /// </summary>
    public bool EnableSsl { get; set; } = false;

    /// <summary>
    /// SSL证书路径
    /// </summary>
    public string? CertificatePath { get; set; }

    /// <summary>
    /// SSL证书密码
    /// </summary>
    public string? CertificatePassword { get; set; }

    /// <summary>
    /// SSL监听端口
    /// </summary>
    public int SslPort { get; set; } = 8883;

    /// <summary>
    /// WebSocket SSL监听端口
    /// </summary>
    public int WebSocketSslPort { get; set; } = 8084;

    /// <summary>
    /// 最大客户端连接数
    /// </summary>
    public int MaxConnections { get; set; } = 10000;

    /// <summary>
    /// 每个客户端最大待处理消息数
    /// </summary>
    public int MaxPendingMessagesPerClient { get; set; } = 250;

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    public int ConnectionTimeoutSeconds { get; set; } = 60;

    /// <summary>
    /// 心跳间隔（秒）
    /// </summary>
    public int KeepAliveIntervalSeconds { get; set; } = 60;

    /// <summary>
    /// 是否启用消息持久化
    /// </summary>
    public bool EnablePersistence { get; set; } = true;

    /// <summary>
    /// 消息保留时间（小时）
    /// </summary>
    public int MessageRetentionHours { get; set; } = 24;

    /// <summary>
    /// 是否启用客户端认证
    /// </summary>
    public bool EnableAuthentication { get; set; } = true;

    /// <summary>
    /// 是否启用主题授权
    /// </summary>
    public bool EnableAuthorization { get; set; } = true;

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool EnableVerboseLogging { get; set; } = false;

    /// <summary>
    /// 性能监控配置
    /// </summary>
    public PerformanceOptions Performance { get; set; } = new();

    /// <summary>
    /// 安全配置
    /// </summary>
    public SecurityOptions Security { get; set; } = new();
}

/// <summary>
/// 性能配置选项
/// </summary>
public class PerformanceOptions
{
    /// <summary>
    /// 消息处理线程池大小
    /// </summary>
    public int MessageProcessingThreads { get; set; } = Environment.ProcessorCount * 2;

    /// <summary>
    /// 消息队列容量
    /// </summary>
    public int MessageQueueCapacity { get; set; } = 10000;

    /// <summary>
    /// 批量处理消息数量
    /// </summary>
    public int BatchProcessingSize { get; set; } = 100;

    /// <summary>
    /// 批量处理超时时间（毫秒）
    /// </summary>
    public int BatchProcessingTimeoutMs { get; set; } = 100;

    /// <summary>
    /// 是否启用消息压缩
    /// </summary>
    public bool EnableCompression { get; set; } = false;

    /// <summary>
    /// 内存池配置
    /// </summary>
    public MemoryPoolOptions MemoryPool { get; set; } = new();
}

/// <summary>
/// 内存池配置选项
/// </summary>
public class MemoryPoolOptions
{
    /// <summary>
    /// 是否启用内存池
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 最大缓冲区大小（字节）
    /// </summary>
    public int MaxBufferSize { get; set; } = 1024 * 1024; // 1MB

    /// <summary>
    /// 缓冲区池大小
    /// </summary>
    public int BufferPoolSize { get; set; } = 1000;
}

/// <summary>
/// 安全配置选项
/// </summary>
public class SecurityOptions
{
    /// <summary>
    /// 允许匿名连接
    /// </summary>
    public bool AllowAnonymousConnections { get; set; } = false;

    /// <summary>
    /// 客户端ID验证模式
    /// </summary>
    public ClientIdValidationMode ClientIdValidation { get; set; } = ClientIdValidationMode.Required;

    /// <summary>
    /// 最大客户端ID长度
    /// </summary>
    public int MaxClientIdLength { get; set; } = 100;

    /// <summary>
    /// 最大用户名长度
    /// </summary>
    public int MaxUsernameLength { get; set; } = 100;

    /// <summary>
    /// 最大密码长度
    /// </summary>
    public int MaxPasswordLength { get; set; } = 100;

    /// <summary>
    /// 最大主题长度
    /// </summary>
    public int MaxTopicLength { get; set; } = 1000;

    /// <summary>
    /// 最大消息负载大小（字节）
    /// </summary>
    public int MaxPayloadSize { get; set; } = 1024 * 1024; // 1MB

    /// <summary>
    /// 登录失败锁定阈值
    /// </summary>
    public int LoginFailureThreshold { get; set; } = 5;

    /// <summary>
    /// 账户锁定时间（分钟）
    /// </summary>
    public int AccountLockoutMinutes { get; set; } = 30;

    /// <summary>
    /// IP黑名单
    /// </summary>
    public List<string> IpBlacklist { get; set; } = new();

    /// <summary>
    /// IP白名单（如果配置，则只允许白名单IP连接）
    /// </summary>
    public List<string> IpWhitelist { get; set; } = new();

    /// <summary>
    /// 是否启用速率限制
    /// </summary>
    public bool EnableRateLimiting { get; set; } = true;

    /// <summary>
    /// 速率限制配置
    /// </summary>
    public RateLimitOptions RateLimit { get; set; } = new();
}

/// <summary>
/// 速率限制配置选项
/// </summary>
public class RateLimitOptions
{
    /// <summary>
    /// 每秒最大连接数
    /// </summary>
    public int MaxConnectionsPerSecond { get; set; } = 100;

    /// <summary>
    /// 每秒最大消息数
    /// </summary>
    public int MaxMessagesPerSecond { get; set; } = 1000;

    /// <summary>
    /// 每秒最大字节数
    /// </summary>
    public int MaxBytesPerSecond { get; set; } = 1024 * 1024; // 1MB

    /// <summary>
    /// 时间窗口大小（秒）
    /// </summary>
    public int WindowSizeSeconds { get; set; } = 60;
}

/// <summary>
/// 客户端ID验证模式
/// </summary>
public enum ClientIdValidationMode
{
    /// <summary>
    /// 不验证
    /// </summary>
    None,

    /// <summary>
    /// 必须提供
    /// </summary>
    Required,

    /// <summary>
    /// 必须唯一
    /// </summary>
    Unique,

    /// <summary>
    /// 自定义验证
    /// </summary>
    Custom
}
