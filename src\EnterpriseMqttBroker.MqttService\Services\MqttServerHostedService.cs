using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using EnterpriseMqttBroker.Core.Interfaces;
using EnterpriseMqttBroker.MqttService.Configuration;
using EnterpriseMqttBroker.MqttService.Interfaces;

namespace EnterpriseMqttBroker.MqttService.Services;

/// <summary>
/// MQTT服务器托管服务
/// </summary>
public class MqttServerHostedService : BackgroundService
{
    private readonly ILogger<MqttServerHostedService> _logger;
    private readonly IMqttServerService _mqttServerService;
    private readonly IConnectionManager _connectionManager;
    private readonly IMessageRouter _messageRouter;
    private readonly MqttServerOptions _options;
    private readonly Timer _cleanupTimer;

    public MqttServerHostedService(
        ILogger<MqttServerHostedService> logger,
        IMqttServerService mqttServerService,
        IConnectionManager connectionManager,
        IMessageRouter messageRouter,
        IOptions<MqttServerOptions> options)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _mqttServerService = mqttServerService ?? throw new ArgumentNullException(nameof(mqttServerService));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _messageRouter = messageRouter ?? throw new ArgumentNullException(nameof(messageRouter));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));

        // 创建清理定时器，每5分钟执行一次
        _cleanupTimer = new Timer(PerformCleanup, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
    }

    /// <summary>
    /// 启动服务
    /// </summary>
    public override async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("正在启动MQTT服务器托管服务...");

        try
        {
            await _mqttServerService.StartAsync(cancellationToken);
            _logger.LogInformation("MQTT服务器托管服务启动成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动MQTT服务器托管服务失败");
            throw;
        }

        await base.StartAsync(cancellationToken);
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("正在停止MQTT服务器托管服务...");

        try
        {
            await _mqttServerService.StopAsync(cancellationToken);
            _cleanupTimer?.Dispose();
            _logger.LogInformation("MQTT服务器托管服务停止成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止MQTT服务器托管服务失败");
        }

        await base.StopAsync(cancellationToken);
    }

    /// <summary>
    /// 执行后台任务
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("MQTT服务器后台任务开始执行");

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                // 监控服务器状态
                await MonitorServerHealthAsync(stoppingToken);

                // 等待一段时间再次检查
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("MQTT服务器后台任务已取消");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MQTT服务器后台任务执行失败");
        }

        _logger.LogInformation("MQTT服务器后台任务执行完成");
    }

    /// <summary>
    /// 监控服务器健康状态
    /// </summary>
    private async Task MonitorServerHealthAsync(CancellationToken cancellationToken)
    {
        try
        {
            if (!_mqttServerService.IsRunning)
            {
                _logger.LogWarning("检测到MQTT服务器未运行，尝试重新启动...");
                await _mqttServerService.StartAsync(cancellationToken);
            }

            // 获取统计信息
            var statistics = await _mqttServerService.GetStatisticsAsync(cancellationToken);
            
            // 记录关键指标
            if (_options.EnableVerboseLogging)
            {
                _logger.LogDebug("服务器统计: 连接数={ConnectedClients}, 消息数={TotalMessages}, 运行时间={Uptime}",
                    statistics.ConnectedClientsCount,
                    statistics.TotalMessagesReceived + statistics.TotalMessagesSent,
                    statistics.Uptime);
            }

            // 检查连接数是否接近限制
            if (statistics.ConnectedClientsCount > _options.MaxConnections * 0.9)
            {
                _logger.LogWarning("连接数接近限制: {CurrentConnections}/{MaxConnections}",
                    statistics.ConnectedClientsCount, _options.MaxConnections);
            }

            // 检查内存使用情况
            var memoryUsage = GC.GetTotalMemory(false);
            if (memoryUsage > 1024 * 1024 * 1024) // 1GB
            {
                _logger.LogWarning("内存使用量较高: {MemoryUsageMB} MB", memoryUsage / 1024 / 1024);
                
                // 触发垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "监控服务器健康状态失败");
        }
    }

    /// <summary>
    /// 执行清理任务
    /// </summary>
    private async void PerformCleanup(object? state)
    {
        try
        {
            _logger.LogDebug("开始执行清理任务...");

            // 清理过期连接
            await _connectionManager.CleanupExpiredConnectionsAsync();

            // 清理过期订阅
            await _messageRouter.CleanupExpiredSubscriptionsAsync();

            // 强制垃圾回收（在低负载时）
            var statistics = await _mqttServerService.GetStatisticsAsync();
            if (statistics.ConnectedClientsCount < _options.MaxConnections * 0.5)
            {
                GC.Collect(2, GCCollectionMode.Optimized);
            }

            _logger.LogDebug("清理任务执行完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行清理任务失败");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public override void Dispose()
    {
        _cleanupTimer?.Dispose();
        base.Dispose();
    }
}
