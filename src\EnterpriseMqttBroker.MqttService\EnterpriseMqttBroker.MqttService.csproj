<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MQTTnet" Version="5.0.1.1416" />
    <PackageReference Include="MQTTnet.AspNetCore" Version="5.0.1.1416" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.6" />
    <PackageReference Include="System.Threading.Channels" Version="9.0.6" />
    <PackageReference Include="System.Security.Cryptography.X509Certificates" Version="4.3.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EnterpriseMqttBroker.Core\EnterpriseMqttBroker.Core.csproj" />
    <ProjectReference Include="..\EnterpriseMqttBroker.Infrastructure\EnterpriseMqttBroker.Infrastructure.csproj" />
  </ItemGroup>

</Project>
