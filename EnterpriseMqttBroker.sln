Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34330.188
MinimumVisualStudioVersion = 10.0.40219.1

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{3AC096D0-A1C2-E12C-1390-A8335801FDAB}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "deployment", "deployment", "{2150E333-8FDC-42A3-9474-1A3956D46DE8}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnterpriseMqttBroker.Core", "src\EnterpriseMqttBroker.Core\EnterpriseMqttBroker.Core.csproj", "{11111111-1111-1111-1111-111111111111}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnterpriseMqttBroker.Infrastructure", "src\EnterpriseMqttBroker.Infrastructure\EnterpriseMqttBroker.Infrastructure.csproj", "{22222222-2222-2222-2222-222222222222}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnterpriseMqttBroker.MqttService", "src\EnterpriseMqttBroker.MqttService\EnterpriseMqttBroker.MqttService.csproj", "{*************-3333-3333-************}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnterpriseMqttBroker.WebApi", "src\EnterpriseMqttBroker.WebApi\EnterpriseMqttBroker.WebApi.csproj", "{*************-4444-4444-************}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnterpriseMqttBroker.UnitTests", "tests\EnterpriseMqttBroker.UnitTests\EnterpriseMqttBroker.UnitTests.csproj", "{*************-5555-5555-************}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnterpriseMqttBroker.IntegrationTests", "tests\EnterpriseMqttBroker.IntegrationTests\EnterpriseMqttBroker.IntegrationTests.csproj", "{*************-6666-6666-************}"
EndProject

Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EnterpriseMqttBroker.LoadTests", "tests\EnterpriseMqttBroker.LoadTests\EnterpriseMqttBroker.LoadTests.csproj", "{*************-7777-7777-************}"
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{11111111-1111-1111-1111-111111111111}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{11111111-1111-1111-1111-111111111111}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{11111111-1111-1111-1111-111111111111}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{11111111-1111-1111-1111-111111111111}.Release|Any CPU.Build.0 = Release|Any CPU
		{22222222-2222-2222-2222-222222222222}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{22222222-2222-2222-2222-222222222222}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{22222222-2222-2222-2222-222222222222}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{22222222-2222-2222-2222-222222222222}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-3333-3333-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-3333-3333-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-3333-3333-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-3333-3333-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-4444-4444-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-4444-4444-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-4444-4444-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-4444-4444-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-5555-5555-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-5555-5555-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-5555-5555-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-5555-5555-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-6666-6666-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-6666-6666-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-6666-6666-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-6666-6666-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-7777-7777-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-7777-7777-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-7777-7777-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-7777-7777-************}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{11111111-1111-1111-1111-111111111111} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{22222222-2222-2222-2222-222222222222} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{*************-3333-3333-************} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{*************-4444-4444-************} = {8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}
		{*************-5555-5555-************} = {3AC096D0-A1C2-E12C-1390-A8335801FDAB}
		{*************-6666-6666-************} = {3AC096D0-A1C2-E12C-1390-A8335801FDAB}
		{*************-7777-7777-************} = {3AC096D0-A1C2-E12C-1390-A8335801FDAB}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-5678-9012-123456789012}
	EndGlobalSection
EndGlobal
