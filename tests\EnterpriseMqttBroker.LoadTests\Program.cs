using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NBomber.CSharp;
using Serilog;

namespace EnterpriseMqttBroker.LoadTests;

/// <summary>
/// 负载测试程序入口点
/// </summary>
public class Program
{
    /// <summary>
    /// 主入口方法
    /// </summary>
    /// <param name="args">命令行参数</param>
    public static async Task Main(string[] args)
    {
        // 配置
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: true)
            .AddCommandLine(args)
            .Build();

        // 日志
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        
        var logger = loggerFactory.CreateLogger<Program>();

        logger.LogInformation("启动 EnterpriseMqttBroker 负载测试");

        try
        {
            // 创建基础负载测试场景
            var scenario = Scenario.Create("mqtt_connection_test", async context =>
            {
                // TODO: 实现MQTT连接和消息发送测试
                await Task.Delay(100);
                return Response.Ok();
            })
            .WithLoadSimulations(
                Simulation.KeepConstant(copies: 10, during: TimeSpan.FromMinutes(1))
            );

            // 运行负载测试
            NBomberRunner
                .RegisterScenarios(scenario)
                .WithLoggerConfig(() => new LoggerConfiguration().WriteTo.Console())
                .Run();

            logger.LogInformation("负载测试完成");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "负载测试执行失败");
        }
    }
}
