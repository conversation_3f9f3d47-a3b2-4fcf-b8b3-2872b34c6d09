using Serilog;
using EnterpriseMqttBroker.Infrastructure.Extensions;

namespace EnterpriseMqttBroker.WebApi;

/// <summary>
/// 应用程序入口点
/// </summary>
public class Program
{
    /// <summary>
    /// 主入口方法
    /// </summary>
    /// <param name="args">命令行参数</param>
    public static async Task Main(string[] args)
    {
        // 配置Serilog
        Log.Logger = new LoggerConfiguration()
            .WriteTo.Console()
            .CreateBootstrapLogger();

        try
        {
            Log.Information("启动 EnterpriseMqttBroker WebApi");

            var builder = WebApplication.CreateBuilder(args);

            // 配置Serilog
            builder.Host.UseSerilog((context, services, configuration) => configuration
                .ReadFrom.Configuration(context.Configuration)
                .ReadFrom.Services(services)
                .Enrich.FromLogContext());

            // 添加服务到容器
            builder.Services.AddControllers();
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen();

            // 添加基础设施服务
            builder.Services.AddInfrastructure(builder.Configuration);
            builder.Services.AddRedisCache(builder.Configuration);
            builder.Services.AddCustomHealthChecks(builder.Configuration);

            var app = builder.Build();

            // 配置HTTP请求管道
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI();
            }

            app.UseHttpsRedirection();
            app.UseAuthorization();
            app.MapControllers();
            app.MapHealthChecks("/health");

            Log.Information("EnterpriseMqttBroker WebApi 启动完成");

            await app.RunAsync();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "应用程序启动失败");
        }
        finally
        {
            await Log.CloseAndFlushAsync();
        }
    }
}
