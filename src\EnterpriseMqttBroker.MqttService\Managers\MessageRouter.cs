using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Caching.Distributed;
using MQTTnet;
using MQTTnet.Server;
using EnterpriseMqttBroker.MqttService.Configuration;
using EnterpriseMqttBroker.MqttService.Interfaces;
using System.Collections.Concurrent;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Diagnostics;

namespace EnterpriseMqttBroker.MqttService.Managers;

/// <summary>
/// 高性能消息路由器实现
/// </summary>
public class MessageRouter : IMessageRouter
{
    private readonly ILogger<MessageRouter> _logger;
    private readonly Configuration.MqttServerOptions _options;
    private readonly IDistributedCache _cache;
    private readonly IConnectionManager _connectionManager;
    
    // 订阅管理：主题过滤器 -> 订阅者列表
    private readonly ConcurrentDictionary<string, ConcurrentDictionary<string, TopicSubscription>> _subscriptions;
    
    // 客户端订阅：客户端ID -> 订阅列表
    private readonly ConcurrentDictionary<string, ConcurrentDictionary<string, TopicSubscription>> _clientSubscriptions;
    
    // 主题统计
    private readonly ConcurrentDictionary<string, TopicStatistics> _topicStatistics;
    
    // 路由统计
    private readonly RoutingStatistics _routingStatistics;
    
    // 性能计数器
    private readonly ConcurrentQueue<double> _routingTimes;
    private readonly Timer _statisticsTimer;
    private readonly object _lockObject = new();

    public MessageRouter(
        ILogger<MessageRouter> logger,
        IOptions<Configuration.MqttServerOptions> options,
        IDistributedCache cache,
        IConnectionManager connectionManager)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        
        _subscriptions = new ConcurrentDictionary<string, ConcurrentDictionary<string, TopicSubscription>>();
        _clientSubscriptions = new ConcurrentDictionary<string, ConcurrentDictionary<string, TopicSubscription>>();
        _topicStatistics = new ConcurrentDictionary<string, TopicStatistics>();
        _routingStatistics = new RoutingStatistics
        {
            LastResetTime = DateTime.UtcNow
        };
        _routingTimes = new ConcurrentQueue<double>();
        
        // 启动统计定时器，每秒更新一次
        _statisticsTimer = new Timer(UpdateStatistics, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
    }

    /// <summary>
    /// 路由消息到订阅者
    /// </summary>
    public async Task RouteMessageAsync(MqttApplicationMessage message, string senderClientId, CancellationToken cancellationToken = default)
    {
        if (message == null)
            throw new ArgumentNullException(nameof(message));

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var topic = message.Topic;
            var routedCount = 0;

            // 更新主题统计
            await UpdateTopicStatisticsAsync(topic, message.PayloadSegment.Count);

            // 查找匹配的订阅者
            var matchingSubscriptions = new List<TopicSubscription>();
            
            foreach (var subscriptionGroup in _subscriptions)
            {
                var topicFilter = subscriptionGroup.Key;
                
                if (IsTopicMatch(topic, topicFilter))
                {
                    foreach (var subscription in subscriptionGroup.Value.Values)
                    {
                        // 排除发送者自己（如果不是共享订阅）
                        if (subscription.ClientId != senderClientId || topicFilter.StartsWith("$share/"))
                        {
                            matchingSubscriptions.Add(subscription);
                        }
                    }
                }
            }

            // 按QoS级别分组处理
            var qosGroups = matchingSubscriptions.GroupBy(s => s.QoSLevel);
            
            foreach (var qosGroup in qosGroups)
            {
                var qos = qosGroup.Key;
                var subscribers = qosGroup.ToList();
                
                // 批量路由消息
                await RouteToSubscribersAsync(message, subscribers, qos, cancellationToken);
                routedCount += subscribers.Count;
            }

            // 更新路由统计
            _routingStatistics.TotalMessagesRouted++;
            _routingStatistics.TopicMessageCounts.AddOrUpdate(topic, 1, (k, v) => v + 1);
            _routingStatistics.QoSLevelCounts.AddOrUpdate(message.QualityOfServiceLevel.GetHashCode(), 1, (k, v) => v + 1);

            stopwatch.Stop();
            _routingTimes.Enqueue(stopwatch.Elapsed.TotalMilliseconds);
            
            // 限制队列大小
            while (_routingTimes.Count > 1000)
            {
                _routingTimes.TryDequeue(out _);
            }

            _logger.LogDebug("消息路由完成: 主题={Topic}, 订阅者={SubscriberCount}, 耗时={ElapsedMs}ms", 
                topic, routedCount, stopwatch.Elapsed.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "消息路由失败: 主题={Topic}", message.Topic);
            throw;
        }
    }

    /// <summary>
    /// 添加订阅
    /// </summary>
    public async Task AddSubscriptionAsync(string clientId, string topicFilter, byte qosLevel, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            throw new ArgumentException("客户端ID不能为空", nameof(clientId));
        
        if (string.IsNullOrEmpty(topicFilter))
            throw new ArgumentException("主题过滤器不能为空", nameof(topicFilter));

        try
        {
            var subscription = new TopicSubscription
            {
                ClientId = clientId,
                TopicFilter = topicFilter,
                QoSLevel = qosLevel,
                SubscribedAt = DateTime.UtcNow,
                LastActivity = DateTime.UtcNow
            };

            // 添加到主题订阅映射
            var topicSubscriptions = _subscriptions.GetOrAdd(topicFilter, 
                _ => new ConcurrentDictionary<string, TopicSubscription>());
            topicSubscriptions.AddOrUpdate(clientId, subscription, (k, v) => subscription);

            // 添加到客户端订阅映射
            var clientSubs = _clientSubscriptions.GetOrAdd(clientId, 
                _ => new ConcurrentDictionary<string, TopicSubscription>());
            clientSubs.AddOrUpdate(topicFilter, subscription, (k, v) => subscription);

            // 持久化到缓存
            await SaveSubscriptionToCacheAsync(subscription, cancellationToken);

            // 更新统计
            _routingStatistics.TotalSubscriptions++;

            _logger.LogDebug("订阅已添加: 客户端={ClientId}, 主题过滤器={TopicFilter}, QoS={QoS}", 
                clientId, topicFilter, qosLevel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加订阅失败: 客户端={ClientId}, 主题过滤器={TopicFilter}", clientId, topicFilter);
            throw;
        }
    }

    /// <summary>
    /// 移除订阅
    /// </summary>
    public async Task RemoveSubscriptionAsync(string clientId, string topicFilter, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(topicFilter))
            return;

        try
        {
            // 从主题订阅映射移除
            if (_subscriptions.TryGetValue(topicFilter, out var topicSubscriptions))
            {
                topicSubscriptions.TryRemove(clientId, out _);
                
                // 如果没有订阅者了，移除整个主题过滤器
                if (topicSubscriptions.IsEmpty)
                {
                    _subscriptions.TryRemove(topicFilter, out _);
                }
            }

            // 从客户端订阅映射移除
            if (_clientSubscriptions.TryGetValue(clientId, out var clientSubs))
            {
                clientSubs.TryRemove(topicFilter, out _);
            }

            // 从缓存移除
            await _cache.RemoveAsync($"subscription:{clientId}:{topicFilter}", cancellationToken);

            // 更新统计
            _routingStatistics.TotalSubscriptions = Math.Max(0, _routingStatistics.TotalSubscriptions - 1);

            _logger.LogDebug("订阅已移除: 客户端={ClientId}, 主题过滤器={TopicFilter}", clientId, topicFilter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除订阅失败: 客户端={ClientId}, 主题过滤器={TopicFilter}", clientId, topicFilter);
        }
    }

    /// <summary>
    /// 移除客户端的所有订阅
    /// </summary>
    public async Task RemoveAllSubscriptionsAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            return;

        try
        {
            if (_clientSubscriptions.TryRemove(clientId, out var clientSubs))
            {
                foreach (var subscription in clientSubs.Values)
                {
                    await RemoveSubscriptionAsync(clientId, subscription.TopicFilter, cancellationToken);
                }
            }

            _logger.LogDebug("客户端所有订阅已移除: 客户端={ClientId}", clientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除客户端所有订阅失败: 客户端={ClientId}", clientId);
        }
    }

    /// <summary>
    /// 获取客户端的订阅列表
    /// </summary>
    public async Task<IEnumerable<TopicSubscription>> GetSubscriptionsAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            return Enumerable.Empty<TopicSubscription>();

        if (_clientSubscriptions.TryGetValue(clientId, out var subscriptions))
        {
            return subscriptions.Values.ToList();
        }

        return Enumerable.Empty<TopicSubscription>();
    }

    /// <summary>
    /// 获取主题的订阅者列表
    /// </summary>
    public async Task<IEnumerable<string>> GetSubscribersAsync(string topic, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(topic))
            return Enumerable.Empty<string>();

        var subscribers = new HashSet<string>();

        foreach (var subscriptionGroup in _subscriptions)
        {
            var topicFilter = subscriptionGroup.Key;
            
            if (IsTopicMatch(topic, topicFilter))
            {
                foreach (var subscription in subscriptionGroup.Value.Values)
                {
                    subscribers.Add(subscription.ClientId);
                }
            }
        }

        return subscribers;
    }

    /// <summary>
    /// 检查主题是否匹配过滤器
    /// </summary>
    public bool IsTopicMatch(string topic, string topicFilter)
    {
        if (string.IsNullOrEmpty(topic) || string.IsNullOrEmpty(topicFilter))
            return false;

        // 完全匹配
        if (topic == topicFilter)
            return true;

        // 处理通配符
        if (topicFilter.Contains('#') || topicFilter.Contains('+'))
        {
            return IsWildcardMatch(topic, topicFilter);
        }

        return false;
    }

    /// <summary>
    /// 高性能通配符匹配算法（避免正则表达式）
    /// </summary>
    private bool IsWildcardMatch(string topic, string topicFilter)
    {
        try
        {
            // 使用字符串分割进行层级匹配，性能更好
            var topicLevels = topic.Split('/');
            var filterLevels = topicFilter.Split('/');

            return MatchLevels(topicLevels, filterLevels, 0, 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "通配符匹配失败: 主题={Topic}, 过滤器={TopicFilter}", topic, topicFilter);
            return false;
        }
    }

    /// <summary>
    /// 递归匹配主题层级
    /// </summary>
    private bool MatchLevels(string[] topicLevels, string[] filterLevels, int topicIndex, int filterIndex)
    {
        // 如果过滤器已经匹配完成
        if (filterIndex >= filterLevels.Length)
        {
            return topicIndex >= topicLevels.Length;
        }

        // 如果主题已经匹配完成，但过滤器还有内容
        if (topicIndex >= topicLevels.Length)
        {
            // 只有当剩余的过滤器都是 # 时才匹配
            return filterIndex == filterLevels.Length - 1 && filterLevels[filterIndex] == "#";
        }

        var currentFilter = filterLevels[filterIndex];

        // 处理多层通配符 #
        if (currentFilter == "#")
        {
            // # 必须是最后一个层级
            return filterIndex == filterLevels.Length - 1;
        }

        // 处理单层通配符 + 或精确匹配
        if (currentFilter == "+" || currentFilter == topicLevels[topicIndex])
        {
            return MatchLevels(topicLevels, filterLevels, topicIndex + 1, filterIndex + 1);
        }

        return false;
    }

    /// <summary>
    /// 路由消息到订阅者列表
    /// </summary>
    private async Task RouteToSubscribersAsync(MqttApplicationMessage message, List<TopicSubscription> subscribers, byte qos, CancellationToken cancellationToken)
    {
        if (subscribers.Count == 0)
            return;

        var tasks = new List<Task>();

        foreach (var subscriber in subscribers)
        {
            // 检查客户端是否仍然连接
            if (_connectionManager.IsConnected(subscriber.ClientId))
            {
                // 更新订阅统计
                subscriber.MessagesReceived++;
                subscriber.LastActivity = DateTime.UtcNow;

                // 异步发送消息（实际发送由MQTTnet处理）
                tasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        await _connectionManager.UpdateLastActivityAsync(subscriber.ClientId, cancellationToken);
                        await _connectionManager.UpdateClientStatisticsAsync(subscriber.ClientId, 0, 1, 0, message.PayloadSegment.Count, cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "更新订阅者统计失败: {ClientId}", subscriber.ClientId);
                    }
                }));
            }
        }

        // 等待所有更新完成
        await Task.WhenAll(tasks);
    }

    /// <summary>
    /// 更新主题统计信息
    /// </summary>
    private async Task UpdateTopicStatisticsAsync(string topic, int payloadSize)
    {
        try
        {
            var now = DateTime.UtcNow;

            var statistics = _topicStatistics.AddOrUpdate(topic,
                new TopicStatistics
                {
                    Topic = topic,
                    MessageCount = 1,
                    TotalBytes = payloadSize,
                    FirstMessageAt = now,
                    LastMessageAt = now,
                    SubscriberCount = await GetSubscriberCountAsync(topic)
                },
                (k, existing) =>
                {
                    existing.MessageCount++;
                    existing.TotalBytes += payloadSize;
                    existing.LastMessageAt = now;

                    // 计算每秒消息数
                    var duration = (now - existing.FirstMessageAt).TotalSeconds;
                    existing.MessagesPerSecond = duration > 0 ? existing.MessageCount / duration : 0;

                    return existing;
                });

            // 定期保存到缓存
            if (statistics.MessageCount % 100 == 0)
            {
                await SaveTopicStatisticsToCacheAsync(topic, statistics);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新主题统计失败: {Topic}", topic);
        }
    }

    /// <summary>
    /// 保存订阅到缓存
    /// </summary>
    private async Task SaveSubscriptionToCacheAsync(TopicSubscription subscription, CancellationToken cancellationToken)
    {
        try
        {
            var json = JsonSerializer.Serialize(subscription);
            var options = new DistributedCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromHours(24)
            };
            await _cache.SetStringAsync($"subscription:{subscription.ClientId}:{subscription.TopicFilter}", json, options, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存订阅到缓存失败: {ClientId}:{TopicFilter}", subscription.ClientId, subscription.TopicFilter);
        }
    }

    /// <summary>
    /// 保存主题统计到缓存
    /// </summary>
    private async Task SaveTopicStatisticsToCacheAsync(string topic, TopicStatistics statistics)
    {
        try
        {
            var json = JsonSerializer.Serialize(statistics);
            var options = new DistributedCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromHours(1)
            };
            await _cache.SetStringAsync($"topic_stats:{topic}", json, options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存主题统计到缓存失败: {Topic}", topic);
        }
    }

    /// <summary>
    /// 获取主题订阅者数量
    /// </summary>
    private async Task<int> GetSubscriberCountAsync(string topic)
    {
        var subscribers = await GetSubscribersAsync(topic);
        return subscribers.Count();
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics(object? state)
    {
        try
        {
            // 计算平均路由时间
            if (_routingTimes.Count > 0)
            {
                var times = _routingTimes.ToArray();
                _routingStatistics.AverageRoutingTimeMs = times.Average();
            }

            // 计算每秒消息数
            var now = DateTime.UtcNow;
            var duration = (now - _routingStatistics.LastResetTime).TotalSeconds;
            if (duration >= 60) // 每分钟重置一次
            {
                _routingStatistics.MessagesPerSecond = (long)(_routingStatistics.TotalMessagesRouted / duration);
                _routingStatistics.LastResetTime = now;
            }

            // 更新活跃主题数
            _routingStatistics.ActiveTopics = _topicStatistics.Count;

            // 更新连接客户端数
            _routingStatistics.ConnectedClients = _connectionManager.GetConnectionCount();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新统计信息失败");
        }
    }

    public async Task<RoutingStatistics> GetRoutingStatisticsAsync(CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(_routingStatistics);
    }

    public async Task CleanupExpiredSubscriptionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var now = DateTime.UtcNow;
            var expiredSubscriptions = new List<(string ClientId, string TopicFilter)>();
            var inactiveThreshold = TimeSpan.FromHours(_options.MessageRetentionHours);

            // 查找过期订阅
            foreach (var clientSubs in _clientSubscriptions)
            {
                var clientId = clientSubs.Key;

                // 检查客户端是否仍然连接
                if (!_connectionManager.IsConnected(clientId))
                {
                    foreach (var subscription in clientSubs.Value.Values)
                    {
                        if (now - subscription.LastActivity > inactiveThreshold)
                        {
                            expiredSubscriptions.Add((clientId, subscription.TopicFilter));
                        }
                    }
                }
            }

            // 清理过期订阅
            foreach (var (clientId, topicFilter) in expiredSubscriptions)
            {
                await RemoveSubscriptionAsync(clientId, topicFilter, cancellationToken);
            }

            // 清理过期主题统计
            var expiredTopics = _topicStatistics
                .Where(kvp => now - kvp.Value.LastMessageAt > TimeSpan.FromDays(1))
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var topic in expiredTopics)
            {
                _topicStatistics.TryRemove(topic, out _);
                await _cache.RemoveAsync($"topic_stats:{topic}", cancellationToken);
            }

            if (expiredSubscriptions.Count > 0 || expiredTopics.Count > 0)
            {
                _logger.LogInformation("清理完成: 过期订阅 {ExpiredSubscriptions} 个, 过期主题 {ExpiredTopics} 个",
                    expiredSubscriptions.Count, expiredTopics.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期订阅失败");
        }
    }

    public async Task<IEnumerable<string>> GetActiveTopicsAsync(CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(_topicStatistics.Keys.ToList());
    }

    public async Task<TopicStatistics?> GetTopicStatisticsAsync(string topic, CancellationToken cancellationToken = default)
    {
        _topicStatistics.TryGetValue(topic, out var statistics);
        return await Task.FromResult(statistics);
    }

    public void Dispose()
    {
        _statisticsTimer?.Dispose();
    }
}
