﻿// <auto-generated />
using System;
using EnterpriseMqttBroker.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace EnterpriseMqttBroker.Infrastructure.Migrations
{
    [DbContext(typeof(MqttBrokerDbContext))]
    [Migration("20250613031501_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("EnterpriseMqttBroker.Core.Models.PersistedMessage", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasComment("消息ID");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ClientId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("客户端ID");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()")
                        .HasComment("创建时间");

                    b.Property<int?>("ExpiryInterval")
                        .HasColumnType("int")
                        .HasComment("消息过期间隔（秒）");

                    b.Property<byte[]>("Payload")
                        .HasColumnType("varbinary(max)")
                        .HasComment("消息载荷");

                    b.Property<byte>("QoSLevel")
                        .HasColumnType("tinyint")
                        .HasComment("QoS级别");

                    b.Property<bool>("Retain")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasComment("是否为保留消息");

                    b.Property<string>("Topic")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasComment("消息主题");

                    b.HasKey("Id");

                    b.HasIndex("ClientId")
                        .HasDatabaseName("IX_PersistedMessages_ClientId");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_PersistedMessages_CreatedAt");

                    b.HasIndex("QoSLevel")
                        .HasDatabaseName("IX_PersistedMessages_QoSLevel");

                    b.HasIndex("Topic")
                        .HasDatabaseName("IX_PersistedMessages_Topic");

                    b.HasIndex("ClientId", "CreatedAt")
                        .HasDatabaseName("IX_PersistedMessages_ClientId_CreatedAt");

                    b.HasIndex("ExpiryInterval", "CreatedAt")
                        .HasDatabaseName("IX_PersistedMessages_ExpiryInterval_CreatedAt")
                        .HasFilter("[ExpiryInterval] IS NOT NULL");

                    b.HasIndex("QoSLevel", "CreatedAt")
                        .HasDatabaseName("IX_PersistedMessages_QoSLevel_CreatedAt");

                    b.HasIndex("Topic", "CreatedAt")
                        .HasDatabaseName("IX_PersistedMessages_Topic_CreatedAt");

                    b.HasIndex("Topic", "Retain")
                        .HasDatabaseName("IX_PersistedMessages_Topic_Retain")
                        .HasFilter("[Retain] = 1");

                    b.ToTable("Mqtt_PersistedMessages", (string)null);
                });

            modelBuilder.Entity("EnterpriseMqttBroker.Core.Models.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWID()");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasComment("角色描述");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("角色名称");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("IX_Roles_Name");

                    b.ToTable("Mqtt_Roles", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("12e30b31-c8d5-4907-8833-dd531dd649ec"),
                            CreatedAt = new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "系统管理员，拥有所有权限",
                            Name = "Administrator"
                        },
                        new
                        {
                            Id = new Guid("2363d76a-2723-4a1b-8d96-a994725b905c"),
                            CreatedAt = new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc),
                            Description = "普通用户，拥有基本MQTT权限",
                            Name = "User"
                        });
                });

            modelBuilder.Entity("EnterpriseMqttBroker.Core.Models.TopicPermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWID()");

                    b.Property<bool>("CanPublish")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasComment("是否允许发布");

                    b.Property<bool>("CanSubscribe")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasComment("是否允许订阅");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("角色ID");

                    b.Property<string>("TopicPattern")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasComment("主题模式（支持通配符）");

                    b.HasKey("Id");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_TopicPermissions_RoleId");

                    b.HasIndex("TopicPattern")
                        .HasDatabaseName("IX_TopicPermissions_TopicPattern");

                    b.HasIndex("RoleId", "TopicPattern")
                        .HasDatabaseName("IX_TopicPermissions_RoleId_TopicPattern");

                    b.HasIndex("TopicPattern", "CanPublish")
                        .HasDatabaseName("IX_TopicPermissions_TopicPattern_CanPublish")
                        .HasFilter("[CanPublish] = 1");

                    b.HasIndex("TopicPattern", "CanSubscribe")
                        .HasDatabaseName("IX_TopicPermissions_TopicPattern_CanSubscribe")
                        .HasFilter("[CanSubscribe] = 1");

                    b.ToTable("Mqtt_TopicPermissions", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("f3611cb9-f0f5-4f93-8b89-8a9ed104658c"),
                            CanPublish = true,
                            CanSubscribe = true,
                            CreatedAt = new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc),
                            RoleId = new Guid("12e30b31-c8d5-4907-8833-dd531dd649ec"),
                            TopicPattern = "#"
                        },
                        new
                        {
                            Id = new Guid("b96a77f5-1674-4199-bb73-361de6d1ccac"),
                            CanPublish = true,
                            CanSubscribe = true,
                            CreatedAt = new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc),
                            RoleId = new Guid("2363d76a-2723-4a1b-8d96-a994725b905c"),
                            TopicPattern = "user/+/data"
                        },
                        new
                        {
                            Id = new Guid("c7969c1b-7072-431a-bbcb-2dc6111860ac"),
                            CanPublish = false,
                            CanSubscribe = true,
                            CreatedAt = new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc),
                            RoleId = new Guid("2363d76a-2723-4a1b-8d96-a994725b905c"),
                            TopicPattern = "public/#"
                        });
                });

            modelBuilder.Entity("EnterpriseMqttBroker.Core.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWID()");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()")
                        .HasComment("创建时间");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasComment("邮箱地址");

                    b.Property<int>("FailedLoginAttempts")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0)
                        .HasComment("失败登录尝试次数");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasComment("是否激活");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("datetime2")
                        .HasComment("最后登录时间");

                    b.Property<DateTime?>("LockoutEndTime")
                        .HasColumnType("datetime2")
                        .HasComment("锁定结束时间");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasComment("密码哈希");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("用户名");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Users_CreatedAt");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("IX_Users_Email")
                        .HasFilter("[Email] IS NOT NULL");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Users_IsActive");

                    b.HasIndex("LastLoginAt")
                        .HasDatabaseName("IX_Users_LastLoginAt");

                    b.HasIndex("LockoutEndTime")
                        .HasDatabaseName("IX_Users_LockoutEndTime")
                        .HasFilter("[LockoutEndTime] IS NOT NULL");

                    b.HasIndex("Username")
                        .IsUnique()
                        .HasDatabaseName("IX_Users_Username");

                    b.ToTable("Mqtt_Users", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("f676f4ff-0509-46e3-8f4f-c2ff4599d21f"),
                            CreatedAt = new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc),
                            Email = "<EMAIL>",
                            FailedLoginAttempts = 0,
                            IsActive = true,
                            PasswordHash = "$2a$11$XwCUWaFSOh6dEHKonXZ1dezlNFB90BiK.K5PIfISTl/l7dZn3cNgq",
                            Username = "admin"
                        });
                });

            modelBuilder.Entity("EnterpriseMqttBroker.Core.Models.UserRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("用户ID");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier")
                        .HasComment("角色ID");

                    b.Property<DateTime>("AssignedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_UserRoles_RoleId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_UserRoles_UserId");

                    b.ToTable("Mqtt_UserRoles", (string)null);

                    b.HasData(
                        new
                        {
                            UserId = new Guid("f676f4ff-0509-46e3-8f4f-c2ff4599d21f"),
                            RoleId = new Guid("12e30b31-c8d5-4907-8833-dd531dd649ec"),
                            AssignedAt = new DateTime(2025, 1, 15, 0, 0, 0, 0, DateTimeKind.Utc)
                        });
                });

            modelBuilder.Entity("EnterpriseMqttBroker.Core.Models.TopicPermission", b =>
                {
                    b.HasOne("EnterpriseMqttBroker.Core.Models.Role", "Role")
                        .WithMany("TopicPermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("EnterpriseMqttBroker.Core.Models.UserRole", b =>
                {
                    b.HasOne("EnterpriseMqttBroker.Core.Models.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EnterpriseMqttBroker.Core.Models.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("EnterpriseMqttBroker.Core.Models.Role", b =>
                {
                    b.Navigation("TopicPermissions");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("EnterpriseMqttBroker.Core.Models.User", b =>
                {
                    b.Navigation("UserRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
