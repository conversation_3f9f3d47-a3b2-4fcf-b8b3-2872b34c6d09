using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MQTTnet.Server;
using EnterpriseMqttBroker.Core.Interfaces;
using EnterpriseMqttBroker.MqttService.Configuration;
using EnterpriseMqttBroker.MqttService.Interfaces;
using System.Text.RegularExpressions;

namespace EnterpriseMqttBroker.MqttService.Security;

/// <summary>
/// MQTT授权处理器实现
/// </summary>
public class AuthorizationHandler : IAuthorizationHandler
{
    private readonly ILogger<AuthorizationHandler> _logger;
    private readonly MqttServerOptions _options;
    private readonly IUserService _userService;
    private readonly AuthorizationStatistics _statistics;

    public AuthorizationHandler(
        ILogger<AuthorizationHandler> logger,
        IOptions<MqttServerOptions> options,
        IUserService userService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _userService = userService ?? throw new ArgumentNullException(nameof(userService));
        _statistics = new AuthorizationStatistics
        {
            LastResetTime = DateTime.UtcNow
        };
    }

    public async Task<AuthorizationResult> ValidatePublishAsync(MqttApplicationMessageInterceptorContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            _statistics.TotalAuthorizationChecks++;
            _statistics.PublishChecks++;

            if (!_options.EnableAuthorization)
            {
                _statistics.SuccessfulAuthorizations++;
                return AuthorizationResult.Allow();
            }

            var username = context.SessionItems.ContainsKey("Username") ? context.SessionItems["Username"]?.ToString() : null;
            var topic = context.ApplicationMessage.Topic;

            var hasPermission = await CanPublishAsync(username, context.ClientId, topic, cancellationToken);
            
            if (hasPermission)
            {
                _statistics.SuccessfulAuthorizations++;
                _statistics.TopicAccessCounts.AddOrUpdate(topic, 1, (k, v) => v + 1);
                return AuthorizationResult.Allow();
            }
            else
            {
                _statistics.FailedAuthorizations++;
                _statistics.FailureReasons.AddOrUpdate("发布权限不足", 1, (k, v) => v + 1);
                return AuthorizationResult.Deny("没有发布权限");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证发布权限失败: 客户端={ClientId}, 主题={Topic}", context.ClientId, context.ApplicationMessage.Topic);
            _statistics.FailedAuthorizations++;
            _statistics.FailureReasons.AddOrUpdate("系统错误", 1, (k, v) => v + 1);
            return AuthorizationResult.Deny("权限验证失败");
        }
    }

    public async Task<AuthorizationResult> ValidateSubscribeAsync(MqttSubscriptionInterceptorContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            _statistics.TotalAuthorizationChecks++;
            _statistics.SubscribeChecks++;

            if (!_options.EnableAuthorization)
            {
                _statistics.SuccessfulAuthorizations++;
                return AuthorizationResult.Allow();
            }

            var username = context.SessionItems.ContainsKey("Username") ? context.SessionItems["Username"]?.ToString() : null;
            var topicFilter = context.TopicFilter.Topic;

            var hasPermission = await CanSubscribeAsync(username, context.ClientId, topicFilter, cancellationToken);
            
            if (hasPermission)
            {
                _statistics.SuccessfulAuthorizations++;
                _statistics.TopicAccessCounts.AddOrUpdate(topicFilter, 1, (k, v) => v + 1);
                return AuthorizationResult.Allow();
            }
            else
            {
                _statistics.FailedAuthorizations++;
                _statistics.FailureReasons.AddOrUpdate("订阅权限不足", 1, (k, v) => v + 1);
                return AuthorizationResult.Deny("没有订阅权限");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证订阅权限失败: 客户端={ClientId}, 主题过滤器={TopicFilter}", context.ClientId, context.TopicFilter.Topic);
            _statistics.FailedAuthorizations++;
            _statistics.FailureReasons.AddOrUpdate("系统错误", 1, (k, v) => v + 1);
            return AuthorizationResult.Deny("权限验证失败");
        }
    }

    public async Task<AuthorizationResult> ValidateUnsubscribeAsync(MqttUnsubscriptionInterceptorContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            _statistics.TotalAuthorizationChecks++;

            // 通常允许取消订阅
            _statistics.SuccessfulAuthorizations++;
            return AuthorizationResult.Allow();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证取消订阅权限失败: 客户端={ClientId}, 主题过滤器={TopicFilter}", context.ClientId, context.Topic);
            _statistics.FailedAuthorizations++;
            return AuthorizationResult.Deny("权限验证失败");
        }
    }

    public async Task<bool> CanPublishAsync(string? username, string clientId, string topic, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(username))
            {
                return _options.Security.AllowAnonymousConnections;
            }

            var permissions = await GetUserTopicPermissionsAsync(username, cancellationToken);
            
            foreach (var permission in permissions)
            {
                if (permission.CanPublish && IsTopicMatchPermissionPattern(topic, permission.TopicPattern))
                {
                    return true;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查发布权限失败: 用户={Username}, 主题={Topic}", username, topic);
            return false;
        }
    }

    public async Task<bool> CanSubscribeAsync(string? username, string clientId, string topicFilter, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(username))
            {
                return _options.Security.AllowAnonymousConnections;
            }

            var permissions = await GetUserTopicPermissionsAsync(username, cancellationToken);
            
            foreach (var permission in permissions)
            {
                if (permission.CanSubscribe && IsTopicMatchPermissionPattern(topicFilter, permission.TopicPattern))
                {
                    return true;
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查订阅权限失败: 用户={Username}, 主题过滤器={TopicFilter}", username, topicFilter);
            return false;
        }
    }

    public async Task<IEnumerable<TopicPermissionInfo>> GetUserTopicPermissionsAsync(string username, CancellationToken cancellationToken = default)
    {
        try
        {
            var roles = await _userService.GetUserRolesAsync(username, cancellationToken);
            var permissions = new List<TopicPermissionInfo>();

            foreach (var role in roles)
            {
                var rolePermissions = await GetRoleTopicPermissionsAsync(role.Name, cancellationToken);
                permissions.AddRange(rolePermissions);
            }

            return permissions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户主题权限失败: {Username}", username);
            return Enumerable.Empty<TopicPermissionInfo>();
        }
    }

    public async Task<IEnumerable<TopicPermissionInfo>> GetRoleTopicPermissionsAsync(string roleName, CancellationToken cancellationToken = default)
    {
        try
        {
            // TODO: 从数据库获取角色权限
            // 这里返回一些默认权限作为示例
            if (roleName == "Administrator")
            {
                return new[]
                {
                    new TopicPermissionInfo
                    {
                        TopicPattern = "#",
                        CanPublish = true,
                        CanSubscribe = true,
                        RoleName = roleName,
                        Description = "管理员全权限"
                    }
                };
            }
            else if (roleName == "User")
            {
                return new[]
                {
                    new TopicPermissionInfo
                    {
                        TopicPattern = "user/+/data",
                        CanPublish = true,
                        CanSubscribe = true,
                        RoleName = roleName,
                        Description = "用户数据权限"
                    },
                    new TopicPermissionInfo
                    {
                        TopicPattern = "public/#",
                        CanPublish = false,
                        CanSubscribe = true,
                        RoleName = roleName,
                        Description = "公共主题订阅权限"
                    }
                };
            }

            return Enumerable.Empty<TopicPermissionInfo>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取角色主题权限失败: {RoleName}", roleName);
            return Enumerable.Empty<TopicPermissionInfo>();
        }
    }

    public bool IsTopicMatchPermissionPattern(string topic, string permissionPattern)
    {
        try
        {
            // 完全匹配
            if (topic == permissionPattern)
                return true;

            // 处理通配符
            if (permissionPattern.Contains('#') || permissionPattern.Contains('+'))
            {
                // 将MQTT通配符转换为正则表达式
                var pattern = permissionPattern
                    .Replace("+", "[^/]+")  // + 匹配单个层级
                    .Replace("#", ".*");    // # 匹配多个层级

                // 确保完全匹配
                pattern = "^" + pattern + "$";

                return Regex.IsMatch(topic, pattern);
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "主题权限匹配失败: 主题={Topic}, 模式={Pattern}", topic, permissionPattern);
            return false;
        }
    }

    public async Task RecordAuthorizationFailureAsync(string? username, string clientId, string action, string topic, string reason, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogWarning("授权失败: 用户={Username}, 客户端={ClientId}, 操作={Action}, 主题={Topic}, 原因={Reason}", 
                username, clientId, action, topic, reason);

            // TODO: 记录到数据库或审计日志
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "记录授权失败异常");
        }
    }

    public async Task<AuthorizationStatistics> GetAuthorizationStatisticsAsync(CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(_statistics);
    }
}
