using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MQTTnet;
using MQTTnet.Server;
using MQTTnet.Protocol;
using EnterpriseMqttBroker.Core.Interfaces;
using EnterpriseMqttBroker.MqttService.Configuration;
using EnterpriseMqttBroker.MqttService.Interfaces;
using EnterpriseMqttBroker.MqttService.Handlers;
using EnterpriseMqttBroker.MqttService.Security;
using System.Collections.Concurrent;
using System.Net;

namespace EnterpriseMqttBroker.MqttService.Managers;

/// <summary>
/// MQTT服务器管理器 - 负责MQTT服务器的生命周期管理
/// </summary>
public class MqttServerManager : IMqttServerService, IDisposable
{
    private readonly ILogger<MqttServerManager> _logger;
    private readonly Configuration.MqttServerOptions _options;
    private readonly IConnectionManager _connectionManager;
    private readonly IMessageRouter _messageRouter;
    private readonly IAuthenticationHandler _authHandler;
    private readonly IAuthorizationHandler _authzHandler;
    private readonly MqttEventHandler _eventHandler;
    
    private MqttServer? _mqttServer;
    private readonly ConcurrentDictionary<string, Core.Interfaces.MqttClientStatus> _clientStatuses;
    private readonly MqttServerStatistics _statistics;
    private readonly object _lockObject = new();
    private bool _disposed;

    public MqttServerManager(
        ILogger<MqttServerManager> logger,
        IOptions<Configuration.MqttServerOptions> options,
        IConnectionManager connectionManager,
        IMessageRouter messageRouter,
        IAuthenticationHandler authHandler,
        IAuthorizationHandler authzHandler)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _messageRouter = messageRouter ?? throw new ArgumentNullException(nameof(messageRouter));
        _authHandler = authHandler ?? throw new ArgumentNullException(nameof(authHandler));
        _authzHandler = authzHandler ?? throw new ArgumentNullException(nameof(authzHandler));

        _eventHandler = new MqttEventHandler(
            logger.CreateLogger<MqttEventHandler>(),
            _connectionManager,
            _messageRouter,
            _authHandler,
            _authzHandler);
        
        _clientStatuses = new ConcurrentDictionary<string, Core.Interfaces.MqttClientStatus>();
        _statistics = new MqttServerStatistics
        {
            StartTime = DateTime.UtcNow
        };
    }

    /// <summary>
    /// 服务器是否正在运行
    /// </summary>
    public bool IsRunning => _mqttServer?.IsStarted == true;

    /// <summary>
    /// 启动MQTT服务器
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(MqttServerManager));

        lock (_lockObject)
        {
            if (IsRunning)
            {
                _logger.LogWarning("MQTT服务器已经在运行中");
                return;
            }
        }

        try
        {
            _logger.LogInformation("正在启动MQTT服务器...");

            // 创建MQTT服务器配置
            var serverOptions = CreateServerOptions();

            // 创建MQTT服务器实例
            var factory = new MqttFactory();
            _mqttServer = factory.CreateMqttServer(serverOptions.Build());

            // 注册事件处理器
            RegisterEventHandlers();

            // 启动服务器
            await _mqttServer.StartAsync();

            _statistics.StartTime = DateTime.UtcNow;
            
            _logger.LogInformation("MQTT服务器启动成功");
            _logger.LogInformation("TCP监听端口: {TcpPort}", _options.TcpPort);
            _logger.LogInformation("WebSocket监听端口: {WebSocketPort}", _options.WebSocketPort);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动MQTT服务器失败");
            throw;
        }
    }

    /// <summary>
    /// 停止MQTT服务器
    /// </summary>
    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;

        lock (_lockObject)
        {
            if (!IsRunning)
            {
                _logger.LogWarning("MQTT服务器未在运行");
                return;
            }
        }

        try
        {
            _logger.LogInformation("正在停止MQTT服务器...");

            if (_mqttServer != null)
            {
                await _mqttServer.StopAsync();
                _mqttServer.Dispose();
                _mqttServer = null;
            }

            _clientStatuses.Clear();
            
            _logger.LogInformation("MQTT服务器已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止MQTT服务器失败");
            throw;
        }
    }

    /// <summary>
    /// 获取连接的客户端列表
    /// </summary>
    public async Task<IEnumerable<Core.Interfaces.MqttClientStatus>> GetConnectedClientsAsync(CancellationToken cancellationToken = default)
    {
        if (!IsRunning || _mqttServer == null)
            return Enumerable.Empty<MqttClientStatus>();

        try
        {
            var clients = await _mqttServer.GetClientsAsync();
            var result = new List<MqttClientStatus>();

            foreach (var client in clients)
            {
                if (_clientStatuses.TryGetValue(client.Id, out var status))
                {
                    // 更新最新信息
                    status.BytesSent = client.Statistics.SentBytes;
                    status.BytesReceived = client.Statistics.ReceivedBytes;
                    status.LastActivity = DateTime.UtcNow;
                    result.Add(status);
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取客户端列表失败");
            return Enumerable.Empty<MqttClientStatus>();
        }
    }

    /// <summary>
    /// 断开指定客户端连接
    /// </summary>
    public async Task DisconnectClientAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            throw new ArgumentException("客户端ID不能为空", nameof(clientId));

        if (!IsRunning || _mqttServer == null)
            throw new InvalidOperationException("MQTT服务器未运行");

        try
        {
            await _mqttServer.DisconnectClientAsync(clientId, MqttDisconnectReasonCode.AdministrativeAction);
            _logger.LogInformation("已断开客户端连接: {ClientId}", clientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "断开客户端连接失败: {ClientId}", clientId);
            throw;
        }
    }

    /// <summary>
    /// 获取服务器统计信息
    /// </summary>
    public async Task<MqttServerStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        if (!IsRunning || _mqttServer == null)
        {
            return new MqttServerStatistics
            {
                StartTime = _statistics.StartTime,
                Uptime = DateTime.UtcNow - _statistics.StartTime
            };
        }

        try
        {
            var clients = await _mqttServer.GetClientsAsync();
            var retainedMessages = await _mqttServer.GetRetainedApplicationMessagesAsync();

            _statistics.ConnectedClientsCount = clients.Count;
            _statistics.RetainedMessagesCount = retainedMessages.Count;
            _statistics.Uptime = DateTime.UtcNow - _statistics.StartTime;

            // 计算总订阅数
            _statistics.TotalSubscriptions = clients.Sum(c => c.Statistics.ReceivedApplicationMessagesCount);

            return _statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取服务器统计信息失败");
            return _statistics;
        }
    }

    /// <summary>
    /// 发布消息到指定主题
    /// </summary>
    public async Task PublishMessageAsync(string topic, byte[] payload, byte qosLevel = 0, bool retain = false, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(topic))
            throw new ArgumentException("主题不能为空", nameof(topic));

        if (!IsRunning || _mqttServer == null)
            throw new InvalidOperationException("MQTT服务器未运行");

        try
        {
            var message = new MqttApplicationMessageBuilder()
                .WithTopic(topic)
                .WithPayload(payload)
                .WithQualityOfServiceLevel((MqttQualityOfServiceLevel)qosLevel)
                .WithRetainFlag(retain)
                .Build();

            await _mqttServer.InjectApplicationMessage(
                new InjectedMqttApplicationMessage(message)
                {
                    SenderClientId = "Server"
                });

            _statistics.TotalMessagesSent++;
            _statistics.TotalBytesSent += payload?.Length ?? 0;

            _logger.LogDebug("已发布消息到主题: {Topic}, QoS: {QoS}, Retain: {Retain}", topic, qosLevel, retain);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布消息失败: {Topic}", topic);
            throw;
        }
    }

    /// <summary>
    /// 获取保留消息列表
    /// </summary>
    public async Task<IEnumerable<MqttApplicationMessage>> GetRetainedMessagesAsync(CancellationToken cancellationToken = default)
    {
        if (!IsRunning || _mqttServer == null)
            return Enumerable.Empty<MqttApplicationMessage>();

        try
        {
            var retainedMessages = await _mqttServer.GetRetainedApplicationMessagesAsync();
            return retainedMessages;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取保留消息失败");
            return Enumerable.Empty<MqttApplicationMessage>();
        }
    }

    /// <summary>
    /// 清除指定主题的保留消息
    /// </summary>
    public async Task ClearRetainedMessageAsync(string topic, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(topic))
            throw new ArgumentException("主题不能为空", nameof(topic));

        if (!IsRunning || _mqttServer == null)
            throw new InvalidOperationException("MQTT服务器未运行");

        try
        {
            await _mqttServer.ClearRetainedApplicationMessagesAsync();
            _logger.LogInformation("已清除主题的保留消息: {Topic}", topic);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除保留消息失败: {Topic}", topic);
            throw;
        }
    }

    /// <summary>
    /// 创建服务器配置选项
    /// </summary>
    private MqttServerOptionsBuilder CreateServerOptions()
    {
        var optionsBuilder = new MqttServerOptionsBuilder()
            .WithDefaultEndpoint()
            .WithDefaultEndpointPort(_options.TcpPort)
            .WithConnectionValidator(OnValidatingConnection)
            .WithApplicationMessageInterceptor(OnInterceptingApplicationMessage)
            .WithSubscriptionInterceptor(OnInterceptingSubscription)
            .WithUnsubscriptionInterceptor(OnInterceptingUnsubscription);

        // 配置WebSocket支持
        if (_options.EnableWebSocket)
        {
            optionsBuilder.WithWebSocketEndpoint($"http://+:{_options.WebSocketPort}/mqtt");
        }

        // 配置SSL/TLS支持
        if (_options.EnableSsl && !string.IsNullOrEmpty(_options.CertificatePath))
        {
            // TODO: 实现SSL证书配置
        }

        // 配置连接限制
        optionsBuilder.WithMaxPendingMessagesPerClient(_options.MaxPendingMessagesPerClient);

        return optionsBuilder;
    }

    /// <summary>
    /// 注册事件处理器
    /// </summary>
    private void RegisterEventHandlers()
    {
        if (_mqttServer == null)
            return;

        _mqttServer.ValidatingConnectionAsync += _eventHandler.OnValidatingConnectionAsync;
        _mqttServer.ClientConnectedAsync += _eventHandler.OnClientConnectedAsync;
        _mqttServer.ClientDisconnectedAsync += _eventHandler.OnClientDisconnectedAsync;
        _mqttServer.InterceptingPublishAsync += _eventHandler.OnInterceptingApplicationMessageAsync;
        _mqttServer.InterceptingSubscriptionAsync += _eventHandler.OnInterceptingSubscriptionAsync;
        _mqttServer.InterceptingUnsubscriptionAsync += _eventHandler.OnInterceptingUnsubscriptionAsync;

        _logger.LogDebug("MQTT事件处理器已注册");
    }

    private Task OnValidatingConnection(MqttConnectionValidatorContext context)
    {
        return _eventHandler.OnValidatingConnectionAsync(context);
    }

    private Task OnInterceptingApplicationMessage(MqttApplicationMessageInterceptorContext context)
    {
        return _eventHandler.OnInterceptingApplicationMessageAsync(context);
    }

    private Task OnInterceptingSubscription(MqttSubscriptionInterceptorContext context)
    {
        return _eventHandler.OnInterceptingSubscriptionAsync(context);
    }

    private Task OnInterceptingUnsubscription(MqttUnsubscriptionInterceptorContext context)
    {
        return _eventHandler.OnInterceptingUnsubscriptionAsync(context);
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _mqttServer?.Dispose();
        _disposed = true;
    }
}
