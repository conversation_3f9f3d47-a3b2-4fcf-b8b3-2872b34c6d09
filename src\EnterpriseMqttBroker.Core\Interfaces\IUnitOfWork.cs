using EnterpriseMqttBroker.Core.Models;

namespace EnterpriseMqttBroker.Core.Interfaces;

/// <summary>
/// 工作单元接口
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// 用户仓储
    /// </summary>
    IRepository<User> Users { get; }

    /// <summary>
    /// 角色仓储
    /// </summary>
    IRepository<Role> Roles { get; }

    /// <summary>
    /// 用户角色仓储
    /// </summary>
    IRepository<UserRole> UserRoles { get; }

    /// <summary>
    /// 主题权限仓储
    /// </summary>
    IRepository<TopicPermission> TopicPermissions { get; }

    /// <summary>
    /// 持久化消息仓储
    /// </summary>
    IRepository<PersistedMessage> PersistedMessages { get; }

    /// <summary>
    /// 保存所有更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 开始事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>事务对象</returns>
    Task<IDbTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 数据库事务接口
/// </summary>
public interface IDbTransaction : IDisposable
{
    /// <summary>
    /// 提交事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task CommitAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 回滚事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task RollbackAsync(CancellationToken cancellationToken = default);
}
