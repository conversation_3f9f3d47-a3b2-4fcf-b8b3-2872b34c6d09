using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using EnterpriseMqttBroker.Core.Interfaces;
using EnterpriseMqttBroker.MqttService.Configuration;
using EnterpriseMqttBroker.MqttService.Interfaces;
using EnterpriseMqttBroker.MqttService.Managers;
using EnterpriseMqttBroker.MqttService.Handlers;
using EnterpriseMqttBroker.MqttService.Security;
using EnterpriseMqttBroker.MqttService.Services;

namespace EnterpriseMqttBroker.MqttService.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加MQTT服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 配置选项
        services.Configure<MqttServerOptions>(configuration.GetSection(MqttServerOptions.SectionName));
        
        // 验证配置
        services.AddSingleton<IValidateOptions<MqttServerOptions>, MqttServerOptionsValidator>();

        // 核心服务
        services.AddSingleton<IMqttServerService, MqttServerManager>();
        services.AddSingleton<IConnectionManager, ConnectionManager>();
        services.AddSingleton<IMessageRouter, MessageRouter>();

        // 认证授权服务
        services.AddScoped<IAuthenticationHandler, AuthenticationHandler>();
        services.AddScoped<IAuthorizationHandler, AuthorizationHandler>();

        // 事件处理器
        services.AddTransient<MqttEventHandler>();

        // 消息批处理器
        services.AddSingleton<MessageBatchProcessor>();

        // 后台服务
        services.AddHostedService<MqttServerHostedService>();

        return services;
    }

    /// <summary>
    /// 添加MQTT服务（带自定义配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项委托</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddMqttServices(this IServiceCollection services, Action<MqttServerOptions> configureOptions)
    {
        // 配置选项
        services.Configure(configureOptions);
        
        // 验证配置
        services.AddSingleton<IValidateOptions<MqttServerOptions>, MqttServerOptionsValidator>();

        // 核心服务
        services.AddSingleton<IMqttServerService, MqttServerManager>();
        services.AddSingleton<IConnectionManager, ConnectionManager>();
        services.AddSingleton<IMessageRouter, MessageRouter>();

        // 认证授权服务
        services.AddScoped<IAuthenticationHandler, AuthenticationHandler>();
        services.AddScoped<IAuthorizationHandler, AuthorizationHandler>();

        // 事件处理器
        services.AddTransient<MqttEventHandler>();

        // 消息批处理器
        services.AddSingleton<MessageBatchProcessor>();

        // 后台服务
        services.AddHostedService<MqttServerHostedService>();

        return services;
    }
}

/// <summary>
/// MQTT服务器配置验证器
/// </summary>
public class MqttServerOptionsValidator : IValidateOptions<MqttServerOptions>
{
    public ValidateOptionsResult Validate(string? name, MqttServerOptions options)
    {
        var failures = new List<string>();

        // 验证端口配置
        if (options.TcpPort <= 0 || options.TcpPort > 65535)
        {
            failures.Add("TCP端口必须在1-65535范围内");
        }

        if (options.EnableWebSocket && (options.WebSocketPort <= 0 || options.WebSocketPort > 65535))
        {
            failures.Add("WebSocket端口必须在1-65535范围内");
        }

        if (options.EnableSsl && (options.SslPort <= 0 || options.SslPort > 65535))
        {
            failures.Add("SSL端口必须在1-65535范围内");
        }

        // 验证SSL配置
        if (options.EnableSsl && string.IsNullOrEmpty(options.CertificatePath))
        {
            failures.Add("启用SSL时必须提供证书路径");
        }

        // 验证连接限制
        if (options.MaxConnections <= 0)
        {
            failures.Add("最大连接数必须大于0");
        }

        if (options.MaxPendingMessagesPerClient <= 0)
        {
            failures.Add("每客户端最大待处理消息数必须大于0");
        }

        // 验证超时配置
        if (options.ConnectionTimeoutSeconds <= 0)
        {
            failures.Add("连接超时时间必须大于0");
        }

        if (options.KeepAliveIntervalSeconds <= 0)
        {
            failures.Add("心跳间隔必须大于0");
        }

        // 验证性能配置
        if (options.Performance.MessageProcessingThreads <= 0)
        {
            failures.Add("消息处理线程数必须大于0");
        }

        if (options.Performance.MessageQueueCapacity <= 0)
        {
            failures.Add("消息队列容量必须大于0");
        }

        // 验证安全配置
        if (options.Security.MaxClientIdLength <= 0)
        {
            failures.Add("最大客户端ID长度必须大于0");
        }

        if (options.Security.MaxPayloadSize <= 0)
        {
            failures.Add("最大消息负载大小必须大于0");
        }

        if (options.Security.LoginFailureThreshold <= 0)
        {
            failures.Add("登录失败锁定阈值必须大于0");
        }

        if (options.Security.AccountLockoutMinutes <= 0)
        {
            failures.Add("账户锁定时间必须大于0");
        }

        // 验证速率限制配置
        if (options.Security.EnableRateLimiting)
        {
            if (options.Security.RateLimit.MaxConnectionsPerSecond <= 0)
            {
                failures.Add("每秒最大连接数必须大于0");
            }

            if (options.Security.RateLimit.MaxMessagesPerSecond <= 0)
            {
                failures.Add("每秒最大消息数必须大于0");
            }

            if (options.Security.RateLimit.WindowSizeSeconds <= 0)
            {
                failures.Add("速率限制时间窗口必须大于0");
            }
        }

        if (failures.Count > 0)
        {
            return ValidateOptionsResult.Fail(failures);
        }

        return ValidateOptionsResult.Success;
    }
}
