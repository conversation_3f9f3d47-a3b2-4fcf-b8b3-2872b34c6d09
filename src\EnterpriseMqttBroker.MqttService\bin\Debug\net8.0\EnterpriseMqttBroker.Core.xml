<?xml version="1.0"?>
<doc>
    <assembly>
        <name>EnterpriseMqttBroker.Core</name>
    </assembly>
    <members>
        <member name="T:EnterpriseMqttBroker.Core.Interfaces.IMessageRepository">
            <summary>
            消息仓储接口
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMessageRepository.GetByClientIdAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            根据客户端ID获取消息
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>消息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMessageRepository.GetByTopicAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            根据主题获取消息
            </summary>
            <param name="topic">主题</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>消息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMessageRepository.GetByTopicPatternAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            根据主题模式获取消息
            </summary>
            <param name="topicPattern">主题模式（支持通配符）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>消息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMessageRepository.GetRetainedMessagesAsync(System.Threading.CancellationToken)">
            <summary>
            获取保留消息
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>保留消息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMessageRepository.GetRetainedMessageByTopicAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            根据主题获取保留消息
            </summary>
            <param name="topic">主题</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>保留消息</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMessageRepository.GetByQoSLevelAsync(System.Byte,System.Threading.CancellationToken)">
            <summary>
            获取指定QoS级别的消息
            </summary>
            <param name="qosLevel">QoS级别</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>消息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMessageRepository.GetByTimeRangeAsync(System.DateTime,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            获取指定时间范围内的消息
            </summary>
            <param name="startTime">开始时间</param>
            <param name="endTime">结束时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>消息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMessageRepository.CleanupExpiredMessagesAsync(System.Threading.CancellationToken)">
            <summary>
            清理过期消息
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>清理的消息数量</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMessageRepository.CleanupOldMessagesAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            清理指定天数之前的消息
            </summary>
            <param name="daysToKeep">保留天数</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>清理的消息数量</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMessageRepository.GetStatisticsAsync(System.Threading.CancellationToken)">
            <summary>
            获取消息统计信息
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>统计信息</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMessageRepository.GetClientStatisticsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            获取客户端消息统计信息
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>统计信息</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMessageRepository.DeleteClientMessagesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            批量删除客户端消息
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>删除的消息数量</returns>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Interfaces.MessageStatistics">
            <summary>
            消息统计信息
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.MessageStatistics.TotalMessages">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.MessageStatistics.RetainedMessages">
            <summary>
            保留消息数
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.MessageStatistics.QoS0Messages">
            <summary>
            QoS 0 消息数
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.MessageStatistics.QoS1Messages">
            <summary>
            QoS 1 消息数
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.MessageStatistics.QoS2Messages">
            <summary>
            QoS 2 消息数
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.MessageStatistics.TodayMessages">
            <summary>
            今日消息数
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.MessageStatistics.AverageMessageSize">
            <summary>
            平均消息大小（字节）
            </summary>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Interfaces.ClientMessageStatistics">
            <summary>
            客户端消息统计信息
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.ClientMessageStatistics.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.ClientMessageStatistics.PublishedMessages">
            <summary>
            发布的消息数
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.ClientMessageStatistics.LastPublishTime">
            <summary>
            最后发布时间
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.ClientMessageStatistics.UniqueTopics">
            <summary>
            发布的主题数
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.ClientMessageStatistics.TotalMessageSize">
            <summary>
            总消息大小（字节）
            </summary>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Interfaces.IMqttServerService">
            <summary>
            MQTT服务器服务接口
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMqttServerService.StartAsync(System.Threading.CancellationToken)">
            <summary>
            启动MQTT服务器
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMqttServerService.StopAsync(System.Threading.CancellationToken)">
            <summary>
            停止MQTT服务器
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.IMqttServerService.IsRunning">
            <summary>
            获取服务器状态
            </summary>
            <returns>是否正在运行</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMqttServerService.GetConnectedClientsAsync(System.Threading.CancellationToken)">
            <summary>
            获取连接的客户端列表
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>客户端信息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMqttServerService.DisconnectClientAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            断开指定客户端连接
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMqttServerService.GetStatisticsAsync(System.Threading.CancellationToken)">
            <summary>
            获取服务器统计信息
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>统计信息</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMqttServerService.PublishMessageAsync(System.String,System.Byte[],System.Byte,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            发布消息到指定主题
            </summary>
            <param name="topic">主题</param>
            <param name="payload">消息负载</param>
            <param name="qosLevel">QoS等级</param>
            <param name="retain">是否保留</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMqttServerService.GetRetainedMessagesAsync(System.Threading.CancellationToken)">
            <summary>
            获取保留消息列表
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>保留消息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IMqttServerService.ClearRetainedMessageAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            清除指定主题的保留消息
            </summary>
            <param name="topic">主题</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Interfaces.MqttClientStatus">
            <summary>
            MQTT客户端状态信息
            </summary>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Interfaces.MqttServerStatistics">
            <summary>
            MQTT服务器统计信息
            </summary>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Interfaces.IRepository`1">
            <summary>
            通用仓储接口
            </summary>
            <typeparam name="T">实体类型</typeparam>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.GetByIdAsync(System.Object,System.Threading.CancellationToken)">
            <summary>
            根据ID获取实体
            </summary>
            <param name="id">实体ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实体对象</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.GetAllAsync(System.Threading.CancellationToken)">
            <summary>
            获取所有实体
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>实体集合</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.FindAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            根据条件查找实体
            </summary>
            <param name="predicate">查询条件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实体集合</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.FirstOrDefaultAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            根据条件查找单个实体
            </summary>
            <param name="predicate">查询条件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实体对象</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.AddAsync(`0,System.Threading.CancellationToken)">
            <summary>
            添加实体
            </summary>
            <param name="entity">实体对象</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>添加的实体</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.AddRangeAsync(System.Collections.Generic.IEnumerable{`0},System.Threading.CancellationToken)">
            <summary>
            批量添加实体
            </summary>
            <param name="entities">实体集合</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.Update(`0)">
            <summary>
            更新实体
            </summary>
            <param name="entity">实体对象</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.UpdateRange(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            批量更新实体
            </summary>
            <param name="entities">实体集合</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.Remove(`0)">
            <summary>
            删除实体
            </summary>
            <param name="entity">实体对象</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.RemoveRange(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            批量删除实体
            </summary>
            <param name="entities">实体集合</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.RemoveByIdAsync(System.Object,System.Threading.CancellationToken)">
            <summary>
            根据ID删除实体
            </summary>
            <param name="id">实体ID</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.ExistsAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            检查实体是否存在
            </summary>
            <param name="predicate">查询条件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>是否存在</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.CountAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            获取实体数量
            </summary>
            <param name="predicate">查询条件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实体数量</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IRepository`1.GetPagedAsync``1(System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean,System.Threading.CancellationToken)">
            <summary>
            分页查询
            </summary>
            <param name="pageIndex">页索引（从0开始）</param>
            <param name="pageSize">页大小</param>
            <param name="predicate">查询条件</param>
            <param name="orderBy">排序表达式</param>
            <param name="ascending">是否升序</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>分页结果</returns>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Interfaces.IUnitOfWork">
            <summary>
            工作单元接口
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.IUnitOfWork.Users">
            <summary>
            用户仓储
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.IUnitOfWork.Roles">
            <summary>
            角色仓储
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.IUnitOfWork.UserRoles">
            <summary>
            用户角色仓储
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.IUnitOfWork.TopicPermissions">
            <summary>
            主题权限仓储
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Interfaces.IUnitOfWork.PersistedMessages">
            <summary>
            持久化消息仓储
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUnitOfWork.SaveChangesAsync(System.Threading.CancellationToken)">
            <summary>
            保存所有更改
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>受影响的行数</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUnitOfWork.BeginTransactionAsync(System.Threading.CancellationToken)">
            <summary>
            开始事务
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>事务对象</returns>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Interfaces.IDbTransaction">
            <summary>
            数据库事务接口
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IDbTransaction.CommitAsync(System.Threading.CancellationToken)">
            <summary>
            提交事务
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IDbTransaction.RollbackAsync(System.Threading.CancellationToken)">
            <summary>
            回滚事务
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Interfaces.IUserRepository">
            <summary>
            用户仓储接口
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserRepository.GetByUsernameWithRolesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            根据用户名获取用户（包含角色信息）
            </summary>
            <param name="username">用户名</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>用户实体</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserRepository.GetByIdWithRolesAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            根据ID获取用户（包含角色信息）
            </summary>
            <param name="userId">用户ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>用户实体</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserRepository.GetByEmailAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            根据邮箱获取用户
            </summary>
            <param name="email">邮箱地址</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>用户实体</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserRepository.IsUsernameExistsAsync(System.String,System.Nullable{System.Guid},System.Threading.CancellationToken)">
            <summary>
            检查用户名是否已存在
            </summary>
            <param name="username">用户名</param>
            <param name="excludeUserId">排除的用户ID（用于更新时检查）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>是否存在</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserRepository.IsEmailExistsAsync(System.String,System.Nullable{System.Guid},System.Threading.CancellationToken)">
            <summary>
            检查邮箱是否已存在
            </summary>
            <param name="email">邮箱地址</param>
            <param name="excludeUserId">排除的用户ID（用于更新时检查）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>是否存在</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserRepository.GetActiveUsersAsync(System.Threading.CancellationToken)">
            <summary>
            获取活跃用户列表
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>活跃用户列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserRepository.GetLockedUsersAsync(System.Threading.CancellationToken)">
            <summary>
            获取被锁定的用户列表
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>被锁定的用户列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserRepository.GetUserPermissionsAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            获取用户的权限列表
            </summary>
            <param name="userId">用户ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>权限列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserRepository.UpdateLastLoginBatchAsync(System.Collections.Generic.IEnumerable{System.Guid},System.DateTime,System.Threading.CancellationToken)">
            <summary>
            批量更新用户最后登录时间
            </summary>
            <param name="userIds">用户ID列表</param>
            <param name="loginTime">登录时间</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserRepository.ClearExpiredLockoutsAsync(System.Threading.CancellationToken)">
            <summary>
            清理过期的锁定用户
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>清理的用户数量</returns>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Interfaces.IUserService">
            <summary>
            用户服务接口
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserService.GetUserByUsernameAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            根据用户名获取用户
            </summary>
            <param name="username">用户名</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>用户实体</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserService.GetUserByIdAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            根据ID获取用户
            </summary>
            <param name="userId">用户ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>用户实体</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserService.CreateUserAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            创建新用户
            </summary>
            <param name="username">用户名</param>
            <param name="password">密码</param>
            <param name="email">邮箱</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>创建的用户实体</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserService.VerifyPassword(EnterpriseMqttBroker.Core.Models.User,System.String)">
            <summary>
            验证用户密码
            </summary>
            <param name="user">用户实体</param>
            <param name="password">密码</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserService.UpdateLastLoginAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            更新用户最后登录时间
            </summary>
            <param name="userId">用户ID</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserService.IncrementFailedLoginAttemptsAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            增加失败登录尝试次数
            </summary>
            <param name="userId">用户ID</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserService.ResetFailedLoginAttemptsAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            重置失败登录尝试次数
            </summary>
            <param name="userId">用户ID</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserService.LockUserAsync(System.Guid,System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            锁定用户账户
            </summary>
            <param name="userId">用户ID</param>
            <param name="lockoutDuration">锁定时长</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserService.UnlockUserAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            解锁用户账户
            </summary>
            <param name="userId">用户ID</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserService.GetUserRolesAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            获取用户的角色列表
            </summary>
            <param name="userId">用户ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>角色列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserService.AssignRoleToUserAsync(System.Guid,System.Guid,System.Threading.CancellationToken)">
            <summary>
            为用户分配角色
            </summary>
            <param name="userId">用户ID</param>
            <param name="roleId">角色ID</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Core.Interfaces.IUserService.RemoveRoleFromUserAsync(System.Guid,System.Guid,System.Threading.CancellationToken)">
            <summary>
            移除用户角色
            </summary>
            <param name="userId">用户ID</param>
            <param name="roleId">角色ID</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Models.PersistedMessage">
            <summary>
            持久化消息实体模型
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.PersistedMessage.Id">
            <summary>
            消息唯一标识
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.PersistedMessage.ClientId">
            <summary>
            客户端ID
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.PersistedMessage.Topic">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.PersistedMessage.Payload">
            <summary>
            消息负载
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.PersistedMessage.QoSLevel">
            <summary>
            QoS等级
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.PersistedMessage.Retain">
            <summary>
            是否为保留消息
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.PersistedMessage.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.PersistedMessage.ExpiryInterval">
            <summary>
            过期间隔（秒）
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.PersistedMessage.IsExpired">
            <summary>
            消息是否已过期
            </summary>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Models.Role">
            <summary>
            角色实体模型
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.Role.Id">
            <summary>
            角色唯一标识
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.Role.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.Role.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.Role.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.Role.UserRoles">
            <summary>
            用户角色关联
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.Role.TopicPermissions">
            <summary>
            主题权限关联
            </summary>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Models.TopicPermission">
            <summary>
            主题权限实体模型
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.TopicPermission.Id">
            <summary>
            权限唯一标识
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.TopicPermission.RoleId">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.TopicPermission.TopicPattern">
            <summary>
            主题模式（支持通配符）
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.TopicPermission.CanPublish">
            <summary>
            是否允许发布
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.TopicPermission.CanSubscribe">
            <summary>
            是否允许订阅
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.TopicPermission.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.TopicPermission.Role">
            <summary>
            角色导航属性
            </summary>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Models.User">
            <summary>
            用户实体模型
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.User.Id">
            <summary>
            用户唯一标识
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.User.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.User.PasswordHash">
            <summary>
            密码哈希
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.User.Email">
            <summary>
            邮箱地址
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.User.IsActive">
            <summary>
            是否激活
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.User.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.User.LastLoginAt">
            <summary>
            最后登录时间
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.User.FailedLoginAttempts">
            <summary>
            失败登录尝试次数
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.User.LockoutEndTime">
            <summary>
            锁定结束时间
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.User.UserRoles">
            <summary>
            用户角色关联
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.User.IsLockedOut">
            <summary>
            检查用户是否被锁定
            </summary>
        </member>
        <member name="T:EnterpriseMqttBroker.Core.Models.UserRole">
            <summary>
            用户角色关联实体
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.UserRole.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.UserRole.RoleId">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.UserRole.AssignedAt">
            <summary>
            分配时间
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.UserRole.User">
            <summary>
            用户导航属性
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Core.Models.UserRole.Role">
            <summary>
            角色导航属性
            </summary>
        </member>
    </members>
</doc>
