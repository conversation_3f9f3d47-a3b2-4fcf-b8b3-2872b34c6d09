using MQTTnet;
using MQTTnet.Server;

namespace EnterpriseMqttBroker.MqttService.Interfaces;

/// <summary>
/// 消息路由器接口
/// </summary>
public interface IMessageRouter
{
    /// <summary>
    /// 路由消息到订阅者
    /// </summary>
    /// <param name="message">MQTT消息</param>
    /// <param name="senderClientId">发送者客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task RouteMessageAsync(MqttApplicationMessage message, string senderClientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 添加订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="qosLevel">QoS等级</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task AddSubscriptionAsync(string clientId, string topicFilter, byte qosLevel, CancellationToken cancellationToken = default);

    /// <summary>
    /// 移除订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task RemoveSubscriptionAsync(string clientId, string topicFilter, CancellationToken cancellationToken = default);

    /// <summary>
    /// 移除客户端的所有订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task RemoveAllSubscriptionsAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端的订阅列表
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅列表</returns>
    Task<IEnumerable<TopicSubscription>> GetSubscriptionsAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取主题的订阅者列表
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅者列表</returns>
    Task<IEnumerable<string>> GetSubscribersAsync(string topic, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查主题是否匹配过滤器
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <returns>是否匹配</returns>
    bool IsTopicMatch(string topic, string topicFilter);

    /// <summary>
    /// 获取路由统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>路由统计信息</returns>
    Task<RoutingStatistics> GetRoutingStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期订阅
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task CleanupExpiredSubscriptionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有活跃主题
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>活跃主题列表</returns>
    Task<IEnumerable<string>> GetActiveTopicsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取主题统计信息
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>主题统计信息</returns>
    Task<TopicStatistics?> GetTopicStatisticsAsync(string topic, CancellationToken cancellationToken = default);
}

/// <summary>
/// 主题订阅信息
/// </summary>
public class TopicSubscription
{
    public string ClientId { get; set; } = string.Empty;
    public string TopicFilter { get; set; } = string.Empty;
    public byte QoSLevel { get; set; }
    public DateTime SubscribedAt { get; set; }
    public DateTime LastActivity { get; set; }
    public long MessagesReceived { get; set; }
}

/// <summary>
/// 路由统计信息
/// </summary>
public class RoutingStatistics
{
    public long TotalMessagesRouted { get; set; }
    public long TotalSubscriptions { get; set; }
    public int ActiveTopics { get; set; }
    public int ConnectedClients { get; set; }
    public double AverageRoutingTimeMs { get; set; }
    public long MessagesPerSecond { get; set; }
    public DateTime LastResetTime { get; set; }
    public Dictionary<string, long> TopicMessageCounts { get; set; } = new();
    public Dictionary<byte, long> QoSLevelCounts { get; set; } = new();
}

/// <summary>
/// 主题统计信息
/// </summary>
public class TopicStatistics
{
    public string Topic { get; set; } = string.Empty;
    public int SubscriberCount { get; set; }
    public long MessageCount { get; set; }
    public long TotalBytes { get; set; }
    public DateTime FirstMessageAt { get; set; }
    public DateTime LastMessageAt { get; set; }
    public double MessagesPerSecond { get; set; }
    public Dictionary<byte, long> QoSDistribution { get; set; } = new();
    public List<string> TopSubscribers { get; set; } = new();
}
