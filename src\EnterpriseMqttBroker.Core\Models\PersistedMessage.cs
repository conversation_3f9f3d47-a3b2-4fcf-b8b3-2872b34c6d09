using System.ComponentModel.DataAnnotations;

namespace EnterpriseMqttBroker.Core.Models;

/// <summary>
/// 持久化消息实体模型
/// </summary>
public class PersistedMessage
{
    /// <summary>
    /// 消息唯一标识
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    [Required]
    [StringLength(100)]
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 主题
    /// </summary>
    [Required]
    [StringLength(255)]
    public string Topic { get; set; } = string.Empty;

    /// <summary>
    /// 消息负载
    /// </summary>
    public byte[]? Payload { get; set; }

    /// <summary>
    /// QoS等级
    /// </summary>
    public byte QoSLevel { get; set; }

    /// <summary>
    /// 是否为保留消息
    /// </summary>
    public bool Retain { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 过期间隔（秒）
    /// </summary>
    public int? ExpiryInterval { get; set; }

    /// <summary>
    /// 消息是否已过期
    /// </summary>
    public bool IsExpired => ExpiryInterval.HasValue && 
                            CreatedAt.AddSeconds(ExpiryInterval.Value) < DateTime.UtcNow;
}
