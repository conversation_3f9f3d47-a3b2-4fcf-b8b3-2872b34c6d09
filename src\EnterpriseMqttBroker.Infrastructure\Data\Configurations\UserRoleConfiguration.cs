using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EnterpriseMqttBroker.Core.Models;

namespace EnterpriseMqttBroker.Infrastructure.Data.Configurations;

/// <summary>
/// 用户角色关联实体配置
/// </summary>
public class UserRoleConfiguration : IEntityTypeConfiguration<UserRole>
{
    /// <summary>
    /// 配置用户角色关联实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<UserRole> builder)
    {
        // 表名
        builder.ToTable("UserRoles");

        // 复合主键
        builder.HasKey(ur => new { ur.UserId, ur.RoleId });

        // 用户ID
        builder.Property(ur => ur.UserId)
            .IsRequired()
            .HasComment("用户ID");

        // 角色ID
        builder.Property(ur => ur.RoleId)
            .IsRequired()
            .HasComment("角色ID");

        // 外键关系
        builder.HasOne(ur => ur.User)
            .WithMany(u => u.UserRoles)
            .HasForeignKey(ur => ur.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ur => ur.Role)
            .WithMany(r => r.UserRoles)
            .HasForeignKey(ur => ur.RoleId)
            .OnDelete(DeleteBehavior.Cascade);

        // 索引
        builder.HasIndex(ur => ur.UserId)
            .HasDatabaseName("IX_UserRoles_UserId");

        builder.HasIndex(ur => ur.RoleId)
            .HasDatabaseName("IX_UserRoles_RoleId");
    }
}
