using Microsoft.EntityFrameworkCore;
using EnterpriseMqttBroker.Core.Models;
using EnterpriseMqttBroker.Infrastructure.Data;
using EnterpriseMqttBroker.Infrastructure.Data.Repositories;
using Xunit;
using FluentAssertions;
using System.Text;

namespace EnterpriseMqttBroker.UnitTests.Infrastructure;

/// <summary>
/// 消息仓储测试
/// </summary>
public class MessageRepositoryTests : IDisposable
{
    private readonly MqttBrokerDbContext _context;
    private readonly MessageRepository _messageRepository;

    public MessageRepositoryTests()
    {
        // 使用内存数据库进行测试
        var options = new DbContextOptionsBuilder<MqttBrokerDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new MqttBrokerDbContext(options);
        _messageRepository = new MessageRepository(_context);
    }

    [Fact]
    public async Task GetByClientIdAsync_ShouldReturnMessagesForClient()
    {
        // Arrange
        var clientId = "test-client";
        var message1 = new PersistedMessage
        {
            ClientId = clientId,
            Topic = "test/topic1",
            Payload = Encoding.UTF8.GetBytes("Hello World 1"),
            QoSLevel = 1,
            Retain = false,
            CreatedAt = DateTime.UtcNow
        };

        var message2 = new PersistedMessage
        {
            ClientId = clientId,
            Topic = "test/topic2",
            Payload = Encoding.UTF8.GetBytes("Hello World 2"),
            QoSLevel = 2,
            Retain = false,
            CreatedAt = DateTime.UtcNow.AddMinutes(-1)
        };

        var otherMessage = new PersistedMessage
        {
            ClientId = "other-client",
            Topic = "test/topic3",
            Payload = Encoding.UTF8.GetBytes("Other message"),
            QoSLevel = 0,
            Retain = false,
            CreatedAt = DateTime.UtcNow
        };

        _context.PersistedMessages.AddRange(message1, message2, otherMessage);
        await _context.SaveChangesAsync();

        // Act
        var result = await _messageRepository.GetByClientIdAsync(clientId);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(m => m.ClientId == clientId);
        result.Should().BeInDescendingOrder(m => m.CreatedAt);
    }

    [Fact]
    public async Task GetByTopicAsync_ShouldReturnMessagesForTopic()
    {
        // Arrange
        var topic = "sensor/temperature";
        var message1 = new PersistedMessage
        {
            ClientId = "client1",
            Topic = topic,
            Payload = Encoding.UTF8.GetBytes("25.5"),
            QoSLevel = 1,
            Retain = false,
            CreatedAt = DateTime.UtcNow
        };

        var message2 = new PersistedMessage
        {
            ClientId = "client2",
            Topic = topic,
            Payload = Encoding.UTF8.GetBytes("26.0"),
            QoSLevel = 1,
            Retain = false,
            CreatedAt = DateTime.UtcNow.AddMinutes(-1)
        };

        var otherMessage = new PersistedMessage
        {
            ClientId = "client1",
            Topic = "sensor/humidity",
            Payload = Encoding.UTF8.GetBytes("60%"),
            QoSLevel = 1,
            Retain = false,
            CreatedAt = DateTime.UtcNow
        };

        _context.PersistedMessages.AddRange(message1, message2, otherMessage);
        await _context.SaveChangesAsync();

        // Act
        var result = await _messageRepository.GetByTopicAsync(topic);

        // Assert
        result.Should().HaveCount(2);
        result.Should().OnlyContain(m => m.Topic == topic);
        result.Should().BeInDescendingOrder(m => m.CreatedAt);
    }

    [Fact]
    public async Task GetRetainedMessagesAsync_ShouldReturnOnlyRetainedMessages()
    {
        // Arrange
        var retainedMessage = new PersistedMessage
        {
            ClientId = "client1",
            Topic = "status/online",
            Payload = Encoding.UTF8.GetBytes("true"),
            QoSLevel = 1,
            Retain = true,
            CreatedAt = DateTime.UtcNow
        };

        var normalMessage = new PersistedMessage
        {
            ClientId = "client1",
            Topic = "data/sensor",
            Payload = Encoding.UTF8.GetBytes("sensor data"),
            QoSLevel = 1,
            Retain = false,
            CreatedAt = DateTime.UtcNow
        };

        _context.PersistedMessages.AddRange(retainedMessage, normalMessage);
        await _context.SaveChangesAsync();

        // Act
        var result = await _messageRepository.GetRetainedMessagesAsync();

        // Assert
        result.Should().HaveCount(1);
        result.First().Should().Be(retainedMessage);
        result.First().Retain.Should().BeTrue();
    }

    [Fact]
    public async Task GetRetainedMessageByTopicAsync_ShouldReturnLatestRetainedMessage()
    {
        // Arrange
        var topic = "status/device1";
        var oldRetainedMessage = new PersistedMessage
        {
            ClientId = "client1",
            Topic = topic,
            Payload = Encoding.UTF8.GetBytes("offline"),
            QoSLevel = 1,
            Retain = true,
            CreatedAt = DateTime.UtcNow.AddHours(-1)
        };

        var newRetainedMessage = new PersistedMessage
        {
            ClientId = "client1",
            Topic = topic,
            Payload = Encoding.UTF8.GetBytes("online"),
            QoSLevel = 1,
            Retain = true,
            CreatedAt = DateTime.UtcNow
        };

        _context.PersistedMessages.AddRange(oldRetainedMessage, newRetainedMessage);
        await _context.SaveChangesAsync();

        // Act
        var result = await _messageRepository.GetRetainedMessageByTopicAsync(topic);

        // Assert
        result.Should().NotBeNull();
        result!.Should().Be(newRetainedMessage);
        Encoding.UTF8.GetString(result.Payload!).Should().Be("online");
    }

    [Fact]
    public async Task GetByQoSLevelAsync_ShouldReturnMessagesWithSpecificQoS()
    {
        // Arrange
        var qos1Message = new PersistedMessage
        {
            ClientId = "client1",
            Topic = "test/qos1",
            Payload = Encoding.UTF8.GetBytes("QoS 1 message"),
            QoSLevel = 1,
            Retain = false,
            CreatedAt = DateTime.UtcNow
        };

        var qos2Message = new PersistedMessage
        {
            ClientId = "client1",
            Topic = "test/qos2",
            Payload = Encoding.UTF8.GetBytes("QoS 2 message"),
            QoSLevel = 2,
            Retain = false,
            CreatedAt = DateTime.UtcNow
        };

        _context.PersistedMessages.AddRange(qos1Message, qos2Message);
        await _context.SaveChangesAsync();

        // Act
        var result = await _messageRepository.GetByQoSLevelAsync(1);

        // Assert
        result.Should().HaveCount(1);
        result.First().QoSLevel.Should().Be(1);
        result.First().Should().Be(qos1Message);
    }

    [Fact]
    public async Task GetStatisticsAsync_ShouldReturnCorrectStatistics()
    {
        // Arrange
        var messages = new[]
        {
            new PersistedMessage
            {
                ClientId = "client1",
                Topic = "test/topic1",
                Payload = Encoding.UTF8.GetBytes("Message 1"),
                QoSLevel = 0,
                Retain = false,
                CreatedAt = DateTime.UtcNow
            },
            new PersistedMessage
            {
                ClientId = "client1",
                Topic = "test/topic2",
                Payload = Encoding.UTF8.GetBytes("Message 2"),
                QoSLevel = 1,
                Retain = true,
                CreatedAt = DateTime.UtcNow
            },
            new PersistedMessage
            {
                ClientId = "client2",
                Topic = "test/topic3",
                Payload = Encoding.UTF8.GetBytes("Message 3"),
                QoSLevel = 2,
                Retain = false,
                CreatedAt = DateTime.UtcNow
            }
        };

        _context.PersistedMessages.AddRange(messages);
        await _context.SaveChangesAsync();

        // Act
        var result = await _messageRepository.GetStatisticsAsync();

        // Assert
        result.TotalMessages.Should().Be(3);
        result.RetainedMessages.Should().Be(1);
        result.QoS0Messages.Should().Be(1);
        result.QoS1Messages.Should().Be(1);
        result.QoS2Messages.Should().Be(1);
        result.TodayMessages.Should().Be(3); // 所有消息都是今天的
        result.AverageMessageSize.Should().BeGreaterThan(0);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
