using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MQTTnet;
using MQTTnet.Server;
using EnterpriseMqttBroker.MqttService.Configuration;
using EnterpriseMqttBroker.MqttService.Interfaces;
using System.Collections.Concurrent;
using System.Threading.Channels;

namespace EnterpriseMqttBroker.MqttService.Managers;

/// <summary>
/// 高性能消息批处理器
/// 基于MQTTnet 5.x最佳实践实现
/// </summary>
public class MessageBatchProcessor : IDisposable
{
    private readonly ILogger<MessageBatchProcessor> _logger;
    private readonly Configuration.MqttServerOptions _options;
    private readonly IMessageRouter _messageRouter;
    
    // 使用Channel进行高性能消息队列
    private readonly Channel<MessageBatch> _messageChannel;
    private readonly ChannelWriter<MessageBatch> _messageWriter;
    private readonly ChannelReader<MessageBatch> _messageReader;
    
    // 批处理任务
    private readonly Task _processingTask;
    private readonly CancellationTokenSource _cancellationTokenSource;
    
    // 性能统计
    private readonly ConcurrentDictionary<string, long> _topicMessageCounts;
    private long _totalProcessedMessages;
    private long _totalProcessedBatches;
    
    public MessageBatchProcessor(
        ILogger<MessageBatchProcessor> logger,
        IOptions<Configuration.MqttServerOptions> options,
        IMessageRouter messageRouter)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _messageRouter = messageRouter ?? throw new ArgumentNullException(nameof(messageRouter));
        
        // 创建高性能Channel
        var channelOptions = new BoundedChannelOptions(_options.Performance.MessageQueueCapacity)
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = true,
            SingleWriter = false,
            AllowSynchronousContinuations = false
        };
        
        _messageChannel = Channel.CreateBounded<MessageBatch>(channelOptions);
        _messageWriter = _messageChannel.Writer;
        _messageReader = _messageChannel.Reader;
        
        _topicMessageCounts = new ConcurrentDictionary<string, long>();
        _cancellationTokenSource = new CancellationTokenSource();
        
        // 启动批处理任务
        _processingTask = Task.Run(ProcessMessagesAsync, _cancellationTokenSource.Token);
        
        _logger.LogInformation("消息批处理器已启动，队列容量: {QueueCapacity}, 批处理大小: {BatchSize}",
            _options.Performance.MessageQueueCapacity, _options.Performance.BatchProcessingSize);
    }
    
    /// <summary>
    /// 添加消息到批处理队列
    /// </summary>
    public async Task<bool> EnqueueMessageAsync(MqttApplicationMessage message, string senderClientId, CancellationToken cancellationToken = default)
    {
        try
        {
            var batch = new MessageBatch
            {
                Message = message,
                SenderClientId = senderClientId,
                EnqueuedAt = DateTime.UtcNow
            };
            
            return await _messageWriter.WriteAsync(batch, cancellationToken);
        }
        catch (InvalidOperationException)
        {
            // Channel已关闭
            return false;
        }
        catch (OperationCanceledException)
        {
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加消息到批处理队列失败: {Topic}", message.Topic);
            return false;
        }
    }
    
    /// <summary>
    /// 批处理消息处理主循环
    /// </summary>
    private async Task ProcessMessagesAsync()
    {
        var batches = new List<MessageBatch>(_options.Performance.BatchProcessingSize);
        var batchTimeout = TimeSpan.FromMilliseconds(_options.Performance.BatchProcessingTimeoutMs);
        
        _logger.LogInformation("消息批处理循环已启动");
        
        try
        {
            while (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                batches.Clear();
                
                // 收集批处理消息
                await CollectBatchAsync(batches, batchTimeout);
                
                if (batches.Count > 0)
                {
                    await ProcessBatchAsync(batches);
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("消息批处理循环已取消");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "消息批处理循环发生错误");
        }
        
        _logger.LogInformation("消息批处理循环已结束");
    }
    
    /// <summary>
    /// 收集批处理消息
    /// </summary>
    private async Task CollectBatchAsync(List<MessageBatch> batches, TimeSpan timeout)
    {
        using var timeoutCts = new CancellationTokenSource(timeout);
        using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
            _cancellationTokenSource.Token, timeoutCts.Token);
        
        try
        {
            // 等待第一个消息
            if (await _messageReader.WaitToReadAsync(combinedCts.Token))
            {
                // 快速收集批处理消息
                while (batches.Count < _options.Performance.BatchProcessingSize && 
                       _messageReader.TryRead(out var batch))
                {
                    batches.Add(batch);
                }
            }
        }
        catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested)
        {
            // 超时，处理已收集的消息
        }
    }
    
    /// <summary>
    /// 处理消息批次
    /// </summary>
    private async Task ProcessBatchAsync(List<MessageBatch> batches)
    {
        try
        {
            var tasks = new List<Task>(batches.Count);
            
            // 并行处理批次中的消息
            foreach (var batch in batches)
            {
                tasks.Add(ProcessSingleMessageAsync(batch));
            }
            
            await Task.WhenAll(tasks);
            
            // 更新统计信息
            Interlocked.Add(ref _totalProcessedMessages, batches.Count);
            Interlocked.Increment(ref _totalProcessedBatches);
            
            // 更新主题统计
            foreach (var batch in batches)
            {
                _topicMessageCounts.AddOrUpdate(batch.Message.Topic, 1, (k, v) => v + 1);
            }
            
            _logger.LogDebug("批处理完成: {BatchSize} 条消息", batches.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理消息批次失败");
        }
    }
    
    /// <summary>
    /// 处理单个消息
    /// </summary>
    private async Task ProcessSingleMessageAsync(MessageBatch batch)
    {
        try
        {
            await _messageRouter.RouteMessageAsync(batch.Message, batch.SenderClientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理单个消息失败: {Topic}", batch.Message.Topic);
        }
    }
    
    /// <summary>
    /// 获取批处理器统计信息
    /// </summary>
    public BatchProcessorStatistics GetStatistics()
    {
        return new BatchProcessorStatistics
        {
            TotalProcessedMessages = _totalProcessedMessages,
            TotalProcessedBatches = _totalProcessedBatches,
            QueueLength = _messageReader.CanCount ? _messageReader.Count : -1,
            TopicMessageCounts = new Dictionary<string, long>(_topicMessageCounts),
            AverageMessagesPerBatch = _totalProcessedBatches > 0 
                ? (double)_totalProcessedMessages / _totalProcessedBatches 
                : 0
        };
    }
    
    /// <summary>
    /// 停止批处理器
    /// </summary>
    public async Task StopAsync()
    {
        _logger.LogInformation("正在停止消息批处理器...");
        
        // 标记写入器完成
        _messageWriter.Complete();
        
        // 取消处理任务
        _cancellationTokenSource.Cancel();
        
        try
        {
            await _processingTask;
        }
        catch (OperationCanceledException)
        {
            // 预期的取消
        }
        
        _logger.LogInformation("消息批处理器已停止");
    }
    
    public void Dispose()
    {
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource?.Dispose();
        _processingTask?.Dispose();
    }
}

/// <summary>
/// 消息批次项
/// </summary>
public class MessageBatch
{
    public MqttApplicationMessage Message { get; set; } = null!;
    public string SenderClientId { get; set; } = string.Empty;
    public DateTime EnqueuedAt { get; set; }
}

/// <summary>
/// 批处理器统计信息
/// </summary>
public class BatchProcessorStatistics
{
    public long TotalProcessedMessages { get; set; }
    public long TotalProcessedBatches { get; set; }
    public int QueueLength { get; set; }
    public Dictionary<string, long> TopicMessageCounts { get; set; } = new();
    public double AverageMessagesPerBatch { get; set; }
}
