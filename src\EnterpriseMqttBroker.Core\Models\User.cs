using System.ComponentModel.DataAnnotations;

namespace EnterpriseMqttBroker.Core.Models;

/// <summary>
/// 用户实体模型
/// </summary>
public class User
{
    /// <summary>
    /// 用户唯一标识
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 用户名
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 密码哈希
    /// </summary>
    [Required]
    [StringLength(255)]
    public string PasswordHash { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱地址
    /// </summary>
    [StringLength(100)]
    [EmailAddress]
    public string? Email { get; set; }

    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 最后登录时间
    /// </summary>
    public DateTime? LastLoginAt { get; set; }

    /// <summary>
    /// 失败登录尝试次数
    /// </summary>
    public int FailedLoginAttempts { get; set; } = 0;

    /// <summary>
    /// 锁定结束时间
    /// </summary>
    public DateTime? LockoutEndTime { get; set; }

    /// <summary>
    /// 用户角色关联
    /// </summary>
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

    /// <summary>
    /// 检查用户是否被锁定
    /// </summary>
    public bool IsLockedOut => LockoutEndTime.HasValue && LockoutEndTime.Value > DateTime.UtcNow;
}
