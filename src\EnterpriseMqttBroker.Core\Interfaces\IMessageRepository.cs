using EnterpriseMqttBroker.Core.Models;

namespace EnterpriseMqttBroker.Core.Interfaces;

/// <summary>
/// 消息仓储接口
/// </summary>
public interface IMessageRepository : IRepository<PersistedMessage>
{
    /// <summary>
    /// 根据客户端ID获取消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息列表</returns>
    Task<IEnumerable<PersistedMessage>> GetByClientIdAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据主题获取消息
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息列表</returns>
    Task<IEnumerable<PersistedMessage>> GetByTopicAsync(string topic, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据主题模式获取消息
    /// </summary>
    /// <param name="topicPattern">主题模式（支持通配符）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息列表</returns>
    Task<IEnumerable<PersistedMessage>> GetByTopicPatternAsync(string topicPattern, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取保留消息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保留消息列表</returns>
    Task<IEnumerable<PersistedMessage>> GetRetainedMessagesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据主题获取保留消息
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保留消息</returns>
    Task<PersistedMessage?> GetRetainedMessageByTopicAsync(string topic, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取指定QoS级别的消息
    /// </summary>
    /// <param name="qosLevel">QoS级别</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息列表</returns>
    Task<IEnumerable<PersistedMessage>> GetByQoSLevelAsync(byte qosLevel, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取指定时间范围内的消息
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息列表</returns>
    Task<IEnumerable<PersistedMessage>> GetByTimeRangeAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期消息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的消息数量</returns>
    Task<int> CleanupExpiredMessagesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理指定天数之前的消息
    /// </summary>
    /// <param name="daysToKeep">保留天数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的消息数量</returns>
    Task<int> CleanupOldMessagesAsync(int daysToKeep, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取消息统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<MessageStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端消息统计信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<ClientMessageStatistics> GetClientStatisticsAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量删除客户端消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除的消息数量</returns>
    Task<int> DeleteClientMessagesAsync(string clientId, CancellationToken cancellationToken = default);
}

/// <summary>
/// 消息统计信息
/// </summary>
public class MessageStatistics
{
    /// <summary>
    /// 总消息数
    /// </summary>
    public long TotalMessages { get; set; }

    /// <summary>
    /// 保留消息数
    /// </summary>
    public long RetainedMessages { get; set; }

    /// <summary>
    /// QoS 0 消息数
    /// </summary>
    public long QoS0Messages { get; set; }

    /// <summary>
    /// QoS 1 消息数
    /// </summary>
    public long QoS1Messages { get; set; }

    /// <summary>
    /// QoS 2 消息数
    /// </summary>
    public long QoS2Messages { get; set; }

    /// <summary>
    /// 今日消息数
    /// </summary>
    public long TodayMessages { get; set; }

    /// <summary>
    /// 平均消息大小（字节）
    /// </summary>
    public double AverageMessageSize { get; set; }
}

/// <summary>
/// 客户端消息统计信息
/// </summary>
public class ClientMessageStatistics
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 发布的消息数
    /// </summary>
    public long PublishedMessages { get; set; }

    /// <summary>
    /// 最后发布时间
    /// </summary>
    public DateTime? LastPublishTime { get; set; }

    /// <summary>
    /// 发布的主题数
    /// </summary>
    public int UniqueTopics { get; set; }

    /// <summary>
    /// 总消息大小（字节）
    /// </summary>
    public long TotalMessageSize { get; set; }
}
