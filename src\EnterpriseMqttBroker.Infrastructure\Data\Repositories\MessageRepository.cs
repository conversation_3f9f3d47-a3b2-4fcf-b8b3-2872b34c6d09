using Microsoft.EntityFrameworkCore;
using EnterpriseMqttBroker.Core.Interfaces;
using EnterpriseMqttBroker.Core.Models;

namespace EnterpriseMqttBroker.Infrastructure.Data.Repositories;

/// <summary>
/// 消息仓储实现
/// </summary>
public class MessageRepository : Repository<PersistedMessage>, IMessageRepository
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="context">数据库上下文</param>
    public MessageRepository(MqttBrokerDbContext context) : base(context)
    {
    }

    /// <summary>
    /// 根据客户端ID获取消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息列表</returns>
    public async Task<IEnumerable<PersistedMessage>> GetByClientIdAsync(string clientId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(pm => pm.ClientId == clientId)
            .OrderByDescending(pm => pm.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// 根据主题获取消息
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息列表</returns>
    public async Task<IEnumerable<PersistedMessage>> GetByTopicAsync(string topic, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(pm => pm.Topic == topic)
            .OrderByDescending(pm => pm.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// 根据主题模式获取消息
    /// </summary>
    /// <param name="topicPattern">主题模式（支持通配符）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息列表</returns>
    public async Task<IEnumerable<PersistedMessage>> GetByTopicPatternAsync(string topicPattern, CancellationToken cancellationToken = default)
    {
        // 将MQTT通配符转换为SQL LIKE模式
        var sqlPattern = topicPattern
            .Replace("+", "%")  // 单级通配符
            .Replace("#", "%"); // 多级通配符

        return await _dbSet
            .Where(pm => EF.Functions.Like(pm.Topic, sqlPattern))
            .OrderByDescending(pm => pm.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// 获取保留消息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保留消息列表</returns>
    public async Task<IEnumerable<PersistedMessage>> GetRetainedMessagesAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(pm => pm.Retain)
            .OrderBy(pm => pm.Topic)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// 根据主题获取保留消息
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保留消息</returns>
    public async Task<PersistedMessage?> GetRetainedMessageByTopicAsync(string topic, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(pm => pm.Topic == topic && pm.Retain)
            .OrderByDescending(pm => pm.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);
    }

    /// <summary>
    /// 获取指定QoS级别的消息
    /// </summary>
    /// <param name="qosLevel">QoS级别</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息列表</returns>
    public async Task<IEnumerable<PersistedMessage>> GetByQoSLevelAsync(byte qosLevel, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(pm => pm.QoSLevel == qosLevel)
            .OrderByDescending(pm => pm.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// 获取指定时间范围内的消息
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>消息列表</returns>
    public async Task<IEnumerable<PersistedMessage>> GetByTimeRangeAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(pm => pm.CreatedAt >= startTime && pm.CreatedAt <= endTime)
            .OrderByDescending(pm => pm.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// 清理过期消息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的消息数量</returns>
    public async Task<int> CleanupExpiredMessagesAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        return await _dbSet
            .Where(pm => pm.ExpiryInterval.HasValue && 
                        EF.Functions.DateDiffSecond(pm.CreatedAt, now) > pm.ExpiryInterval.Value)
            .ExecuteDeleteAsync(cancellationToken);
    }

    /// <summary>
    /// 清理指定天数之前的消息
    /// </summary>
    /// <param name="daysToKeep">保留天数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的消息数量</returns>
    public async Task<int> CleanupOldMessagesAsync(int daysToKeep, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
        return await _dbSet
            .Where(pm => pm.CreatedAt < cutoffDate && !pm.Retain) // 保留消息不清理
            .ExecuteDeleteAsync(cancellationToken);
    }

    /// <summary>
    /// 获取消息统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    public async Task<MessageStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var today = DateTime.UtcNow.Date;
        var tomorrow = today.AddDays(1);

        var stats = await _dbSet
            .GroupBy(pm => 1)
            .Select(g => new MessageStatistics
            {
                TotalMessages = g.Count(),
                RetainedMessages = g.Count(pm => pm.Retain),
                QoS0Messages = g.Count(pm => pm.QoSLevel == 0),
                QoS1Messages = g.Count(pm => pm.QoSLevel == 1),
                QoS2Messages = g.Count(pm => pm.QoSLevel == 2),
                TodayMessages = g.Count(pm => pm.CreatedAt >= today && pm.CreatedAt < tomorrow),
                AverageMessageSize = g.Average(pm => pm.Payload != null ? pm.Payload.Length : 0)
            })
            .FirstOrDefaultAsync(cancellationToken);

        return stats ?? new MessageStatistics();
    }

    /// <summary>
    /// 获取客户端消息统计信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    public async Task<ClientMessageStatistics> GetClientStatisticsAsync(string clientId, CancellationToken cancellationToken = default)
    {
        var stats = await _dbSet
            .Where(pm => pm.ClientId == clientId)
            .GroupBy(pm => pm.ClientId)
            .Select(g => new ClientMessageStatistics
            {
                ClientId = g.Key,
                PublishedMessages = g.Count(),
                LastPublishTime = g.Max(pm => pm.CreatedAt),
                UniqueTopics = g.Select(pm => pm.Topic).Distinct().Count(),
                TotalMessageSize = g.Sum(pm => pm.Payload != null ? pm.Payload.Length : 0)
            })
            .FirstOrDefaultAsync(cancellationToken);

        return stats ?? new ClientMessageStatistics { ClientId = clientId };
    }

    /// <summary>
    /// 批量删除客户端消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除的消息数量</returns>
    public async Task<int> DeleteClientMessagesAsync(string clientId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(pm => pm.ClientId == clientId)
            .ExecuteDeleteAsync(cancellationToken);
    }
}
