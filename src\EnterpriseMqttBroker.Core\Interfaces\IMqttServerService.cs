using MQTTnet;

namespace EnterpriseMqttBroker.Core.Interfaces;

/// <summary>
/// MQTT服务器服务接口
/// </summary>
public interface IMqttServerService
{
    /// <summary>
    /// 启动MQTT服务器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止MQTT服务器
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取服务器状态
    /// </summary>
    /// <returns>是否正在运行</returns>
    bool IsRunning { get; }

    /// <summary>
    /// 获取连接的客户端列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>客户端信息列表</returns>
    Task<IEnumerable<MqttClientStatus>> GetConnectedClientsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 断开指定客户端连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task DisconnectClientAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取服务器统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<MqttServerStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 发布消息到指定主题
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="payload">消息负载</param>
    /// <param name="qosLevel">QoS等级</param>
    /// <param name="retain">是否保留</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task PublishMessageAsync(string topic, byte[] payload, byte qosLevel = 0, bool retain = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取保留消息列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保留消息列表</returns>
    Task<IEnumerable<MqttApplicationMessage>> GetRetainedMessagesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 清除指定主题的保留消息
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task ClearRetainedMessageAsync(string topic, CancellationToken cancellationToken = default);
}

/// <summary>
/// MQTT客户端状态信息
/// </summary>
public class MqttClientStatus
{
    public string ClientId { get; set; } = string.Empty;
    public string Endpoint { get; set; } = string.Empty;
    public DateTime ConnectedAt { get; set; }
    public long BytesSent { get; set; }
    public long BytesReceived { get; set; }
    public int SubscriptionsCount { get; set; }
    public DateTime LastActivity { get; set; }
    public string ProtocolVersion { get; set; } = string.Empty;
}

/// <summary>
/// MQTT服务器统计信息
/// </summary>
public class MqttServerStatistics
{
    public int ConnectedClientsCount { get; set; }
    public long TotalMessagesReceived { get; set; }
    public long TotalMessagesSent { get; set; }
    public long TotalBytesReceived { get; set; }
    public long TotalBytesSent { get; set; }
    public int TotalSubscriptions { get; set; }
    public int RetainedMessagesCount { get; set; }
    public DateTime StartTime { get; set; }
    public TimeSpan Uptime { get; set; }
}
