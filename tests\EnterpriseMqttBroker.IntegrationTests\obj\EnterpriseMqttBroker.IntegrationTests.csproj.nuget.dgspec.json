{"format": 1, "restore": {"D:\\01 Broker\\tests\\EnterpriseMqttBroker.IntegrationTests\\EnterpriseMqttBroker.IntegrationTests.csproj": {}}, "projects": {"D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\EnterpriseMqttBroker.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\EnterpriseMqttBroker.Core.csproj", "projectName": "EnterpriseMqttBroker.Core", "projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\EnterpriseMqttBroker.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MQTTnet": {"target": "Package", "version": "[5.0.1.1416, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\01 Broker\\src\\EnterpriseMqttBroker.Infrastructure\\EnterpriseMqttBroker.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Infrastructure\\EnterpriseMqttBroker.Infrastructure.csproj", "projectName": "EnterpriseMqttBroker.Infrastructure", "projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Infrastructure\\EnterpriseMqttBroker.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\EnterpriseMqttBroker.Core.csproj": {"projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\EnterpriseMqttBroker.Core.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AspNetCore.HealthChecks.Redis": {"target": "Package", "version": "[8.0.1, )"}, "AspNetCore.HealthChecks.SqlServer": {"target": "Package", "version": "[8.0.2, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"target": "Package", "version": "[8.0.12, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": {"target": "Package", "version": "[8.0.12, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "Serilog": {"target": "Package", "version": "[4.3.0, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[7.0.0, )"}, "Serilog.Sinks.Seq": {"target": "Package", "version": "[9.0.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.8.41, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\01 Broker\\src\\EnterpriseMqttBroker.MqttService\\EnterpriseMqttBroker.MqttService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\01 Broker\\src\\EnterpriseMqttBroker.MqttService\\EnterpriseMqttBroker.MqttService.csproj", "projectName": "EnterpriseMqttBroker.MqttService", "projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.MqttService\\EnterpriseMqttBroker.MqttService.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.MqttService\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\EnterpriseMqttBroker.Core.csproj": {"projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\EnterpriseMqttBroker.Core.csproj"}, "D:\\01 Broker\\src\\EnterpriseMqttBroker.Infrastructure\\EnterpriseMqttBroker.Infrastructure.csproj": {"projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Infrastructure\\EnterpriseMqttBroker.Infrastructure.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MQTTnet": {"target": "Package", "version": "[5.0.1.1416, )"}, "MQTTnet.AspNetCore": {"target": "Package", "version": "[5.0.1.1416, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.6, )"}, "System.Security.Cryptography.X509Certificates": {"target": "Package", "version": "[4.3.2, )"}, "System.Threading.Channels": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\01 Broker\\src\\EnterpriseMqttBroker.WebApi\\EnterpriseMqttBroker.WebApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\01 Broker\\src\\EnterpriseMqttBroker.WebApi\\EnterpriseMqttBroker.WebApi.csproj", "projectName": "EnterpriseMqttBroker.WebApi", "projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.WebApi\\EnterpriseMqttBroker.WebApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.WebApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\EnterpriseMqttBroker.Core.csproj": {"projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\EnterpriseMqttBroker.Core.csproj"}, "D:\\01 Broker\\src\\EnterpriseMqttBroker.Infrastructure\\EnterpriseMqttBroker.Infrastructure.csproj": {"projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Infrastructure\\EnterpriseMqttBroker.Infrastructure.csproj"}, "D:\\01 Broker\\src\\EnterpriseMqttBroker.MqttService\\EnterpriseMqttBroker.MqttService.csproj": {"projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.MqttService\\EnterpriseMqttBroker.MqttService.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Asp.Versioning.Mvc": {"target": "Package", "version": "[8.1.0, )"}, "Asp.Versioning.Mvc.ApiExplorer": {"target": "Package", "version": "[8.1.0, )"}, "AspNetCore.HealthChecks.Redis": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.12, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.12, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"target": "Package", "version": "[8.0.12, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": {"target": "Package", "version": "[8.0.12, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.2.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.3.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\01 Broker\\tests\\EnterpriseMqttBroker.IntegrationTests\\EnterpriseMqttBroker.IntegrationTests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\01 Broker\\tests\\EnterpriseMqttBroker.IntegrationTests\\EnterpriseMqttBroker.IntegrationTests.csproj", "projectName": "EnterpriseMqttBroker.IntegrationTests", "projectPath": "D:\\01 Broker\\tests\\EnterpriseMqttBroker.IntegrationTests\\EnterpriseMqttBroker.IntegrationTests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\01 Broker\\tests\\EnterpriseMqttBroker.IntegrationTests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\EnterpriseMqttBroker.Core.csproj": {"projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\EnterpriseMqttBroker.Core.csproj"}, "D:\\01 Broker\\src\\EnterpriseMqttBroker.Infrastructure\\EnterpriseMqttBroker.Infrastructure.csproj": {"projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Infrastructure\\EnterpriseMqttBroker.Infrastructure.csproj"}, "D:\\01 Broker\\src\\EnterpriseMqttBroker.MqttService\\EnterpriseMqttBroker.MqttService.csproj": {"projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.MqttService\\EnterpriseMqttBroker.MqttService.csproj"}, "D:\\01 Broker\\src\\EnterpriseMqttBroker.WebApi\\EnterpriseMqttBroker.WebApi.csproj": {"projectPath": "D:\\01 Broker\\src\\EnterpriseMqttBroker.WebApi\\EnterpriseMqttBroker.WebApi.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentAssertions": {"target": "Package", "version": "[7.0.0, )"}, "MQTTnet": {"target": "Package", "version": "[5.0.1.1416, )"}, "Microsoft.AspNetCore.Mvc.Testing": {"target": "Package", "version": "[8.0.12, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.12.0, )"}, "Testcontainers": {"target": "Package", "version": "[4.1.0, )"}, "Testcontainers.MsSql": {"target": "Package", "version": "[4.1.0, )"}, "Testcontainers.Redis": {"target": "Package", "version": "[4.1.0, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.2, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}