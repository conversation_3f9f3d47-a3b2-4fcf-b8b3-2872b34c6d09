{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Information", "EnterpriseMqttBroker": "Debug"}}, "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=EnterpriseMqttBrokerDb_Dev;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true", "Redis": "localhost:6379"}, "MqttSettings": {"MaxConnections": 1000, "AllowAnonymousConnections": true}, "SecuritySettings": {"RequireHttps": false, "MaxFailedLoginAttempts": 10, "LockoutDurationMinutes": 5}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information", "Microsoft.EntityFrameworkCore": "Information", "EnterpriseMqttBroker": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext} {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/mqtt-broker-dev-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext} {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}}