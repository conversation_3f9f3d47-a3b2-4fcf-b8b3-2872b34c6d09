using MQTTnet.Server;

namespace EnterpriseMqttBroker.MqttService.Interfaces;

/// <summary>
/// MQTT认证处理器接口
/// </summary>
public interface IAuthenticationHandler
{
    /// <summary>
    /// 验证客户端连接
    /// </summary>
    /// <param name="context">连接验证上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<AuthenticationResult> ValidateConnectionAsync(MqttConnectionValidatorContext context, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证用户凭据
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="endpoint">客户端终结点</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<AuthenticationResult> ValidateCredentialsAsync(string? username, string? password, string clientId, string endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证客户端证书
    /// </summary>
    /// <param name="certificate">客户端证书</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<AuthenticationResult> ValidateCertificateAsync(byte[]? certificate, string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查客户端ID是否有效
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否有效</returns>
    Task<bool> IsValidClientIdAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查客户端是否被锁定
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="endpoint">客户端终结点</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>锁定信息</returns>
    Task<LockoutInfo> CheckLockoutAsync(string? username, string clientId, string endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// 记录登录失败
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="endpoint">客户端终结点</param>
    /// <param name="reason">失败原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task RecordLoginFailureAsync(string? username, string clientId, string endpoint, string reason, CancellationToken cancellationToken = default);

    /// <summary>
    /// 记录登录成功
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="endpoint">客户端终结点</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task RecordLoginSuccessAsync(string? username, string clientId, string endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户角色
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户角色列表</returns>
    Task<IEnumerable<string>> GetUserRolesAsync(string username, CancellationToken cancellationToken = default);
}

/// <summary>
/// 认证结果
/// </summary>
public class AuthenticationResult
{
    public bool IsSuccess { get; set; }
    public string? Username { get; set; }
    public string? UserId { get; set; }
    public IEnumerable<string> Roles { get; set; } = Enumerable.Empty<string>();
    public string? FailureReason { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
    
    public static AuthenticationResult Success(string? username, string? userId, IEnumerable<string>? roles = null)
    {
        return new AuthenticationResult
        {
            IsSuccess = true,
            Username = username,
            UserId = userId,
            Roles = roles ?? Enumerable.Empty<string>()
        };
    }
    
    public static AuthenticationResult Failure(string reason)
    {
        return new AuthenticationResult
        {
            IsSuccess = false,
            FailureReason = reason
        };
    }
}

/// <summary>
/// 锁定信息
/// </summary>
public class LockoutInfo
{
    public bool IsLockedOut { get; set; }
    public DateTime? LockoutEndTime { get; set; }
    public int FailedAttempts { get; set; }
    public string? Reason { get; set; }
    public TimeSpan? RemainingLockoutTime => LockoutEndTime.HasValue ? 
        LockoutEndTime.Value - DateTime.UtcNow : null;
}
