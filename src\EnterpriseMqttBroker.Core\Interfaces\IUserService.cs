using EnterpriseMqttBroker.Core.Models;

namespace EnterpriseMqttBroker.Core.Interfaces;

/// <summary>
/// 用户服务接口
/// </summary>
public interface IUserService
{
    /// <summary>
    /// 根据用户名获取用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    Task<User?> GetUserByUsernameAsync(string username, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID获取用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    Task<User?> GetUserByIdAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 创建新用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="email">邮箱</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的用户实体</returns>
    Task<User> CreateUserAsync(string username, string password, string? email = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证用户密码
    /// </summary>
    /// <param name="user">用户实体</param>
    /// <param name="password">密码</param>
    /// <returns>验证结果</returns>
    bool VerifyPassword(User user, string password);

    /// <summary>
    /// 更新用户最后登录时间
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task UpdateLastLoginAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 增加失败登录尝试次数
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task IncrementFailedLoginAttemptsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 重置失败登录尝试次数
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task ResetFailedLoginAttemptsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 锁定用户账户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="lockoutDuration">锁定时长</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task LockUserAsync(Guid userId, TimeSpan lockoutDuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// 解锁用户账户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task UnlockUserAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户的角色列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>角色列表</returns>
    Task<IEnumerable<Role>> GetUserRolesAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 为用户分配角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleId">角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task AssignRoleToUserAsync(Guid userId, Guid roleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 移除用户角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleId">角色ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task RemoveRoleFromUserAsync(Guid userId, Guid roleId, CancellationToken cancellationToken = default);
}
