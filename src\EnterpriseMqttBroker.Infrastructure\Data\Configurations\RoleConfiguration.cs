using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EnterpriseMqttBroker.Core.Models;

namespace EnterpriseMqttBroker.Infrastructure.Data.Configurations;

/// <summary>
/// 角色实体配置
/// </summary>
public class RoleConfiguration : IEntityTypeConfiguration<Role>
{
    /// <summary>
    /// 配置角色实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<Role> builder)
    {
        // 表名
        builder.ToTable("Roles");

        // 主键
        builder.HasKey(r => r.Id);
        builder.Property(r => r.Id)
            .HasDefaultValueSql("NEWID()");

        // 角色名称
        builder.Property(r => r.Name)
            .IsRequired()
            .HasMaxLength(50)
            .HasComment("角色名称");

        // 角色描述
        builder.Property(r => r.Description)
            .HasMaxLength(200)
            .HasComment("角色描述");

        // 导航属性
        builder.HasMany(r => r.UserRoles)
            .WithOne(ur => ur.Role)
            .HasForeignKey(ur => ur.RoleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(r => r.TopicPermissions)
            .WithOne(tp => tp.Role)
            .HasForeignKey(tp => tp.RoleId)
            .OnDelete(DeleteBehavior.Cascade);

        // 唯一约束
        builder.HasIndex(r => r.Name)
            .IsUnique()
            .HasDatabaseName("IX_Roles_Name");
    }
}
