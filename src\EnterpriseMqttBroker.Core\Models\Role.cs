using System.ComponentModel.DataAnnotations;

namespace EnterpriseMqttBroker.Core.Models;

/// <summary>
/// 角色实体模型
/// </summary>
public class Role
{
    /// <summary>
    /// 角色唯一标识
    /// </summary>
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// 角色名称
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 角色描述
    /// </summary>
    [StringLength(200)]
    public string? Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 用户角色关联
    /// </summary>
    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

    /// <summary>
    /// 主题权限关联
    /// </summary>
    public virtual ICollection<TopicPermission> TopicPermissions { get; set; } = new List<TopicPermission>();
}
