<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <!-- 启用WebSocket支持 -->
      <webSocket enabled="true" />
      
      <!-- ASP.NET Core Module配置 -->
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      
      <aspNetCore processPath="dotnet" 
                  arguments=".\EnterpriseMqttBroker.WebApi.dll" 
                  stdoutLogEnabled="false" 
                  stdoutLogFile=".\logs\stdout" 
                  hostingModel="inprocess">
        <environmentVariables>
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
          <environmentVariable name="ASPNETCORE_HTTPS_PORT" value="443" />
        </environmentVariables>
      </aspNetCore>
      
      <!-- 安全头设置 -->
      <httpProtocol>
        <customHeaders>
          <add name="X-Content-Type-Options" value="nosniff" />
          <add name="X-Frame-Options" value="DENY" />
          <add name="X-XSS-Protection" value="1; mode=block" />
          <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
          <add name="Content-Security-Policy" value="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; connect-src 'self' ws: wss:;" />
        </customHeaders>
      </httpProtocol>
      
      <!-- 压缩设置 -->
      <httpCompression directory="%SystemDrive%\inetpub\temp\IIS Temporary Compressed Files">
        <scheme name="gzip" dll="%Windir%\system32\inetsrv\gzip.dll" />
        <dynamicTypes>
          <add mimeType="text/*" enabled="true" />
          <add mimeType="message/*" enabled="true" />
          <add mimeType="application/javascript" enabled="true" />
          <add mimeType="application/json" enabled="true" />
          <add mimeType="*/*" enabled="false" />
        </dynamicTypes>
        <staticTypes>
          <add mimeType="text/*" enabled="true" />
          <add mimeType="message/*" enabled="true" />
          <add mimeType="application/javascript" enabled="true" />
          <add mimeType="application/json" enabled="true" />
          <add mimeType="*/*" enabled="false" />
        </staticTypes>
      </httpCompression>
      
      <!-- URL重写规则 -->
      <rewrite>
        <rules>
          <!-- HTTPS重定向 -->
          <rule name="Redirect to HTTPS" stopProcessing="true">
            <match url="(.*)" />
            <conditions>
              <add input="{HTTPS}" pattern="off" ignoreCase="true" />
              <add input="{HTTP_HOST}" pattern="localhost" negate="true" />
            </conditions>
            <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="Permanent" />
          </rule>
        </rules>
      </rewrite>
      
      <!-- 错误页面 -->
      <httpErrors errorMode="Custom" defaultResponseMode="ExecuteURL">
        <remove statusCode="404" />
        <error statusCode="404" responseMode="ExecuteURL" path="/Error/NotFound" />
        <remove statusCode="500" />
        <error statusCode="500" responseMode="ExecuteURL" path="/Error/InternalServerError" />
      </httpErrors>
      
      <!-- 静态文件缓存 -->
      <staticContent>
        <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="30.00:00:00" />
      </staticContent>
      
      <!-- 请求过滤 -->
      <security>
        <requestFiltering>
          <requestLimits maxAllowedContentLength="52428800" maxUrl="4096" maxQueryString="2048" />
          <hiddenSegments>
            <add segment="bin" />
            <add segment="App_code" />
            <add segment="App_GlobalResources" />
            <add segment="App_LocalResources" />
            <add segment="App_WebReferences" />
            <add segment="App_Data" />
            <add segment="App_Browsers" />
          </hiddenSegments>
        </requestFiltering>
      </security>
    </system.webServer>
  </location>
  
  <!-- 系统Web配置 -->
  <system.web>
    <httpRuntime executionTimeout="3600" maxRequestLength="51200" enableVersionHeader="false" />
    <compilation debug="false" targetFramework="4.8" />
    <customErrors mode="RemoteOnly" defaultRedirect="~/Error" />
    <sessionState mode="Off" />
    <httpCookies httpOnlyCookies="true" requireSSL="true" sameSite="Strict" />
  </system.web>
</configuration>
