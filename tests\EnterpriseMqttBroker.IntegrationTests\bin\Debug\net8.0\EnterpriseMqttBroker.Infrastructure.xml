<?xml version="1.0"?>
<doc>
    <assembly>
        <name>EnterpriseMqttBroker.Infrastructure</name>
    </assembly>
    <members>
        <member name="T:EnterpriseMqttBroker.Infrastructure.Data.Configurations.PersistedMessageConfiguration">
            <summary>
            持久化消息实体配置
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Configurations.PersistedMessageConfiguration.Configure(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder{EnterpriseMqttBroker.Core.Models.PersistedMessage})">
            <summary>
            配置持久化消息实体
            </summary>
            <param name="builder">实体类型构建器</param>
        </member>
        <member name="T:EnterpriseMqttBroker.Infrastructure.Data.Configurations.RoleConfiguration">
            <summary>
            角色实体配置
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Configurations.RoleConfiguration.Configure(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder{EnterpriseMqttBroker.Core.Models.Role})">
            <summary>
            配置角色实体
            </summary>
            <param name="builder">实体类型构建器</param>
        </member>
        <member name="T:EnterpriseMqttBroker.Infrastructure.Data.Configurations.TopicPermissionConfiguration">
            <summary>
            主题权限实体配置
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Configurations.TopicPermissionConfiguration.Configure(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder{EnterpriseMqttBroker.Core.Models.TopicPermission})">
            <summary>
            配置主题权限实体
            </summary>
            <param name="builder">实体类型构建器</param>
        </member>
        <member name="T:EnterpriseMqttBroker.Infrastructure.Data.Configurations.UserConfiguration">
            <summary>
            用户实体配置
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Configurations.UserConfiguration.Configure(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder{EnterpriseMqttBroker.Core.Models.User})">
            <summary>
            配置用户实体
            </summary>
            <param name="builder">实体类型构建器</param>
        </member>
        <member name="T:EnterpriseMqttBroker.Infrastructure.Data.Configurations.UserRoleConfiguration">
            <summary>
            用户角色关联实体配置
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Configurations.UserRoleConfiguration.Configure(Microsoft.EntityFrameworkCore.Metadata.Builders.EntityTypeBuilder{EnterpriseMqttBroker.Core.Models.UserRole})">
            <summary>
            配置用户角色关联实体
            </summary>
            <param name="builder">实体类型构建器</param>
        </member>
        <member name="T:EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext">
            <summary>
            MQTT Broker 数据库上下文
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext.#ctor(Microsoft.EntityFrameworkCore.DbContextOptions{EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext})">
            <summary>
            构造函数
            </summary>
            <param name="options">数据库上下文选项</param>
        </member>
        <member name="P:EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext.Users">
            <summary>
            用户表
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext.Roles">
            <summary>
            角色表
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext.UserRoles">
            <summary>
            用户角色关联表
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext.TopicPermissions">
            <summary>
            主题权限表
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext.PersistedMessages">
            <summary>
            持久化消息表
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext.OnModelCreating(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            配置实体模型
            </summary>
            <param name="modelBuilder">模型构建器</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext.ConfigureIndexes(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            配置索引
            </summary>
            <param name="modelBuilder">模型构建器</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext.SeedData(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <summary>
            种子数据
            </summary>
            <param name="modelBuilder">模型构建器</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext.SaveChangesAsync(System.Threading.CancellationToken)">
            <summary>
            保存更改前的处理
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>受影响的行数</returns>
        </member>
        <member name="T:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository">
            <summary>
            消息仓储实现
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository.#ctor(EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext)">
            <summary>
            构造函数
            </summary>
            <param name="context">数据库上下文</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository.GetByClientIdAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            根据客户端ID获取消息
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>消息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository.GetByTopicAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            根据主题获取消息
            </summary>
            <param name="topic">主题</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>消息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository.GetByTopicPatternAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            根据主题模式获取消息
            </summary>
            <param name="topicPattern">主题模式（支持通配符）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>消息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository.GetRetainedMessagesAsync(System.Threading.CancellationToken)">
            <summary>
            获取保留消息
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>保留消息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository.GetRetainedMessageByTopicAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            根据主题获取保留消息
            </summary>
            <param name="topic">主题</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>保留消息</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository.GetByQoSLevelAsync(System.Byte,System.Threading.CancellationToken)">
            <summary>
            获取指定QoS级别的消息
            </summary>
            <param name="qosLevel">QoS级别</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>消息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository.GetByTimeRangeAsync(System.DateTime,System.DateTime,System.Threading.CancellationToken)">
            <summary>
            获取指定时间范围内的消息
            </summary>
            <param name="startTime">开始时间</param>
            <param name="endTime">结束时间</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>消息列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository.CleanupExpiredMessagesAsync(System.Threading.CancellationToken)">
            <summary>
            清理过期消息
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>清理的消息数量</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository.CleanupOldMessagesAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            清理指定天数之前的消息
            </summary>
            <param name="daysToKeep">保留天数</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>清理的消息数量</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository.GetStatisticsAsync(System.Threading.CancellationToken)">
            <summary>
            获取消息统计信息
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>统计信息</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository.GetClientStatisticsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            获取客户端消息统计信息
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>统计信息</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.MessageRepository.DeleteClientMessagesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            批量删除客户端消息
            </summary>
            <param name="clientId">客户端ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>删除的消息数量</returns>
        </member>
        <member name="T:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1">
            <summary>
            通用仓储实现
            </summary>
            <typeparam name="T">实体类型</typeparam>
        </member>
        <member name="F:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1._context">
            <summary>
            数据库上下文
            </summary>
        </member>
        <member name="F:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1._dbSet">
            <summary>
            数据库集合
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.#ctor(EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext)">
            <summary>
            构造函数
            </summary>
            <param name="context">数据库上下文</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.GetByIdAsync(System.Object,System.Threading.CancellationToken)">
            <summary>
            根据ID获取实体
            </summary>
            <param name="id">实体ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实体对象</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.GetAllAsync(System.Threading.CancellationToken)">
            <summary>
            获取所有实体
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>实体集合</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.FindAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            根据条件查找实体
            </summary>
            <param name="predicate">查询条件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实体集合</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.FirstOrDefaultAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            根据条件查找单个实体
            </summary>
            <param name="predicate">查询条件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实体对象</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.AddAsync(`0,System.Threading.CancellationToken)">
            <summary>
            添加实体
            </summary>
            <param name="entity">实体对象</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>添加的实体</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.AddRangeAsync(System.Collections.Generic.IEnumerable{`0},System.Threading.CancellationToken)">
            <summary>
            批量添加实体
            </summary>
            <param name="entities">实体集合</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.Update(`0)">
            <summary>
            更新实体
            </summary>
            <param name="entity">实体对象</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.UpdateRange(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            批量更新实体
            </summary>
            <param name="entities">实体集合</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.Remove(`0)">
            <summary>
            删除实体
            </summary>
            <param name="entity">实体对象</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.RemoveRange(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            批量删除实体
            </summary>
            <param name="entities">实体集合</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.RemoveByIdAsync(System.Object,System.Threading.CancellationToken)">
            <summary>
            根据ID删除实体
            </summary>
            <param name="id">实体ID</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.ExistsAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            检查实体是否存在
            </summary>
            <param name="predicate">查询条件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>是否存在</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.CountAsync(System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Threading.CancellationToken)">
            <summary>
            获取实体数量
            </summary>
            <param name="predicate">查询条件</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实体数量</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.Repository`1.GetPagedAsync``1(System.Int32,System.Int32,System.Linq.Expressions.Expression{System.Func{`0,System.Boolean}},System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean,System.Threading.CancellationToken)">
            <summary>
            分页查询
            </summary>
            <param name="pageIndex">页索引（从0开始）</param>
            <param name="pageSize">页大小</param>
            <param name="predicate">查询条件</param>
            <param name="orderBy">排序表达式</param>
            <param name="ascending">是否升序</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>分页结果</returns>
        </member>
        <member name="T:EnterpriseMqttBroker.Infrastructure.Data.Repositories.UserRepository">
            <summary>
            用户仓储实现
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.UserRepository.#ctor(EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext)">
            <summary>
            构造函数
            </summary>
            <param name="context">数据库上下文</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.UserRepository.GetByUsernameWithRolesAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            根据用户名获取用户（包含角色信息）
            </summary>
            <param name="username">用户名</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>用户实体</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.UserRepository.GetByIdWithRolesAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            根据ID获取用户（包含角色信息）
            </summary>
            <param name="userId">用户ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>用户实体</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.UserRepository.GetByEmailAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            根据邮箱获取用户
            </summary>
            <param name="email">邮箱地址</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>用户实体</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.UserRepository.IsUsernameExistsAsync(System.String,System.Nullable{System.Guid},System.Threading.CancellationToken)">
            <summary>
            检查用户名是否已存在
            </summary>
            <param name="username">用户名</param>
            <param name="excludeUserId">排除的用户ID（用于更新时检查）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>是否存在</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.UserRepository.IsEmailExistsAsync(System.String,System.Nullable{System.Guid},System.Threading.CancellationToken)">
            <summary>
            检查邮箱是否已存在
            </summary>
            <param name="email">邮箱地址</param>
            <param name="excludeUserId">排除的用户ID（用于更新时检查）</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>是否存在</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.UserRepository.GetActiveUsersAsync(System.Threading.CancellationToken)">
            <summary>
            获取活跃用户列表
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>活跃用户列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.UserRepository.GetLockedUsersAsync(System.Threading.CancellationToken)">
            <summary>
            获取被锁定的用户列表
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>被锁定的用户列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.UserRepository.GetUserPermissionsAsync(System.Guid,System.Threading.CancellationToken)">
            <summary>
            获取用户的权限列表
            </summary>
            <param name="userId">用户ID</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>权限列表</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.UserRepository.UpdateLastLoginBatchAsync(System.Collections.Generic.IEnumerable{System.Guid},System.DateTime,System.Threading.CancellationToken)">
            <summary>
            批量更新用户最后登录时间
            </summary>
            <param name="userIds">用户ID列表</param>
            <param name="loginTime">登录时间</param>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.Repositories.UserRepository.ClearExpiredLockoutsAsync(System.Threading.CancellationToken)">
            <summary>
            清理过期的锁定用户
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>清理的用户数量</returns>
        </member>
        <member name="T:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork">
            <summary>
            工作单元实现
            </summary>
        </member>
        <member name="F:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork._context">
            <summary>
            数据库上下文
            </summary>
        </member>
        <member name="F:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork._users">
            <summary>
            用户仓储
            </summary>
        </member>
        <member name="F:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork._roles">
            <summary>
            角色仓储
            </summary>
        </member>
        <member name="F:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork._userRoles">
            <summary>
            用户角色仓储
            </summary>
        </member>
        <member name="F:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork._topicPermissions">
            <summary>
            主题权限仓储
            </summary>
        </member>
        <member name="F:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork._persistedMessages">
            <summary>
            持久化消息仓储
            </summary>
        </member>
        <member name="F:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork._disposed">
            <summary>
            是否已释放资源
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork.#ctor(EnterpriseMqttBroker.Infrastructure.Data.MqttBrokerDbContext)">
            <summary>
            构造函数
            </summary>
            <param name="context">数据库上下文</param>
        </member>
        <member name="P:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork.Users">
            <summary>
            用户仓储
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork.Roles">
            <summary>
            角色仓储
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork.UserRoles">
            <summary>
            用户角色仓储
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork.TopicPermissions">
            <summary>
            主题权限仓储
            </summary>
        </member>
        <member name="P:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork.PersistedMessages">
            <summary>
            持久化消息仓储
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork.SaveChangesAsync(System.Threading.CancellationToken)">
            <summary>
            保存所有更改
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>受影响的行数</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork.BeginTransactionAsync(System.Threading.CancellationToken)">
            <summary>
            开始事务
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>事务对象</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.UnitOfWork.Dispose(System.Boolean)">
            <summary>
            释放资源
            </summary>
            <param name="disposing">是否正在释放</param>
        </member>
        <member name="T:EnterpriseMqttBroker.Infrastructure.Data.DbTransactionWrapper">
            <summary>
            数据库事务包装器
            </summary>
        </member>
        <member name="F:EnterpriseMqttBroker.Infrastructure.Data.DbTransactionWrapper._transaction">
            <summary>
            EF Core 事务
            </summary>
        </member>
        <member name="F:EnterpriseMqttBroker.Infrastructure.Data.DbTransactionWrapper._disposed">
            <summary>
            是否已释放资源
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.DbTransactionWrapper.#ctor(Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction)">
            <summary>
            构造函数
            </summary>
            <param name="transaction">EF Core 事务</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.DbTransactionWrapper.CommitAsync(System.Threading.CancellationToken)">
            <summary>
            提交事务
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.DbTransactionWrapper.RollbackAsync(System.Threading.CancellationToken)">
            <summary>
            回滚事务
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.DbTransactionWrapper.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Data.DbTransactionWrapper.Dispose(System.Boolean)">
            <summary>
            释放资源
            </summary>
            <param name="disposing">是否正在释放</param>
        </member>
        <member name="T:EnterpriseMqttBroker.Infrastructure.Extensions.ServiceCollectionExtensions">
            <summary>
            服务集合扩展方法
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Extensions.ServiceCollectionExtensions.AddInfrastructure(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            添加基础设施服务
            </summary>
            <param name="services">服务集合</param>
            <param name="configuration">配置</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Extensions.ServiceCollectionExtensions.AddRedisCache(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            添加Redis缓存服务
            </summary>
            <param name="services">服务集合</param>
            <param name="configuration">配置</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Extensions.ServiceCollectionExtensions.AddCustomHealthChecks(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            添加健康检查
            </summary>
            <param name="services">服务集合</param>
            <param name="configuration">配置</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Extensions.ServiceCollectionExtensions.EnsureDatabaseCreatedAsync(System.IServiceProvider)">
            <summary>
            确保数据库已创建
            </summary>
            <param name="services">服务提供者</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Extensions.ServiceCollectionExtensions.MigrateDatabaseAsync(System.IServiceProvider)">
            <summary>
            运行数据库迁移
            </summary>
            <param name="services">服务提供者</param>
            <returns>异步任务</returns>
        </member>
        <member name="T:EnterpriseMqttBroker.Infrastructure.Migrations.InitialCreate">
            <inheritdoc />
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Migrations.InitialCreate.Up(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Migrations.InitialCreate.Down(Microsoft.EntityFrameworkCore.Migrations.MigrationBuilder)">
            <inheritdoc />
        </member>
        <member name="M:EnterpriseMqttBroker.Infrastructure.Migrations.InitialCreate.BuildTargetModel(Microsoft.EntityFrameworkCore.ModelBuilder)">
            <inheritdoc />
        </member>
    </members>
</doc>
