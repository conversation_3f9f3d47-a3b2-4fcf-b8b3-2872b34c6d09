using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EnterpriseMqttBroker.Core.Models;

namespace EnterpriseMqttBroker.Infrastructure.Data.Configurations;

/// <summary>
/// 用户实体配置
/// </summary>
public class UserConfiguration : IEntityTypeConfiguration<User>
{
    /// <summary>
    /// 配置用户实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<User> builder)
    {
        // 表名
        builder.ToTable("Users");

        // 主键
        builder.HasKey(u => u.Id);
        builder.Property(u => u.Id)
            .HasDefaultValueSql("NEWID()");

        // 用户名
        builder.Property(u => u.Username)
            .IsRequired()
            .HasMaxLength(50)
            .HasComment("用户名");

        // 密码哈希
        builder.Property(u => u.PasswordHash)
            .IsRequired()
            .HasMaxLength(255)
            .HasComment("密码哈希");

        // 邮箱
        builder.Property(u => u.Email)
            .HasMaxLength(100)
            .HasComment("邮箱地址");

        // 是否激活
        builder.Property(u => u.IsActive)
            .IsRequired()
            .HasDefaultValue(true)
            .HasComment("是否激活");

        // 创建时间
        builder.Property(u => u.CreatedAt)
            .IsRequired()
            .HasColumnType("datetime2")
            .HasDefaultValueSql("GETUTCDATE()")
            .HasComment("创建时间");

        // 最后登录时间
        builder.Property(u => u.LastLoginAt)
            .HasColumnType("datetime2")
            .HasComment("最后登录时间");

        // 失败登录尝试次数
        builder.Property(u => u.FailedLoginAttempts)
            .IsRequired()
            .HasDefaultValue(0)
            .HasComment("失败登录尝试次数");

        // 锁定结束时间
        builder.Property(u => u.LockoutEndTime)
            .HasColumnType("datetime2")
            .HasComment("锁定结束时间");

        // 导航属性
        builder.HasMany(u => u.UserRoles)
            .WithOne(ur => ur.User)
            .HasForeignKey(ur => ur.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // 忽略计算属性
        builder.Ignore(u => u.IsLockedOut);

        // 唯一约束
        builder.HasIndex(u => u.Username)
            .IsUnique()
            .HasDatabaseName("IX_Users_Username");

        builder.HasIndex(u => u.Email)
            .IsUnique()
            .HasFilter("[Email] IS NOT NULL")
            .HasDatabaseName("IX_Users_Email");

        // 性能索引
        builder.HasIndex(u => u.IsActive)
            .HasDatabaseName("IX_Users_IsActive");

        builder.HasIndex(u => u.CreatedAt)
            .HasDatabaseName("IX_Users_CreatedAt");

        builder.HasIndex(u => u.LastLoginAt)
            .HasDatabaseName("IX_Users_LastLoginAt");

        builder.HasIndex(u => u.LockoutEndTime)
            .HasFilter("[LockoutEndTime] IS NOT NULL")
            .HasDatabaseName("IX_Users_LockoutEndTime");
    }
}
