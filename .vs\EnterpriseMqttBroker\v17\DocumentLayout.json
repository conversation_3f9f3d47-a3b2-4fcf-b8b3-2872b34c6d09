{"Version": 1, "WorkspaceRootPath": "D:\\01 Broker\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{77777777-7777-7777-7777-777777777777}|tests\\EnterpriseMqttBroker.LoadTests\\EnterpriseMqttBroker.LoadTests.csproj|d:\\01 broker\\tests\\enterprisemqttbroker.loadtests\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{77777777-7777-7777-7777-777777777777}|tests\\EnterpriseMqttBroker.LoadTests\\EnterpriseMqttBroker.LoadTests.csproj|solutionrelative:tests\\enterprisemqttbroker.loadtests\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{44444444-4444-4444-4444-444444444444}|src\\EnterpriseMqttBroker.WebApi\\EnterpriseMqttBroker.WebApi.csproj|d:\\01 broker\\src\\enterprisemqttbroker.webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{44444444-4444-4444-4444-444444444444}|src\\EnterpriseMqttBroker.WebApi\\EnterpriseMqttBroker.WebApi.csproj|solutionrelative:src\\enterprisemqttbroker.webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{11111111-1111-1111-1111-111111111111}|src\\EnterpriseMqttBroker.Core\\EnterpriseMqttBroker.Core.csproj|d:\\01 broker\\src\\enterprisemqttbroker.core\\interfaces\\imqttserverservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{11111111-1111-1111-1111-111111111111}|src\\EnterpriseMqttBroker.Core\\EnterpriseMqttBroker.Core.csproj|solutionrelative:src\\enterprisemqttbroker.core\\interfaces\\imqttserverservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 8, "Children": [{"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "D:\\01 Broker\\tests\\EnterpriseMqttBroker.LoadTests\\Program.cs", "RelativeDocumentMoniker": "tests\\EnterpriseMqttBroker.LoadTests\\Program.cs", "ToolTip": "D:\\01 Broker\\tests\\EnterpriseMqttBroker.LoadTests\\Program.cs", "RelativeToolTip": "tests\\EnterpriseMqttBroker.LoadTests\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T02:46:01.144Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "D:\\01 Broker\\src\\EnterpriseMqttBroker.WebApi\\Program.cs", "RelativeDocumentMoniker": "src\\EnterpriseMqttBroker.WebApi\\Program.cs", "ToolTip": "D:\\01 Broker\\src\\EnterpriseMqttBroker.WebApi\\Program.cs", "RelativeToolTip": "src\\EnterpriseMqttBroker.WebApi\\Program.cs", "ViewState": "AgIAABMAAAAAAAAAAAAIwCoAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T02:45:14.967Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "IMqttServerService.cs", "DocumentMoniker": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\Interfaces\\IMqttServerService.cs", "RelativeDocumentMoniker": "src\\EnterpriseMqttBroker.Core\\Interfaces\\IMqttServerService.cs", "ToolTip": "D:\\01 Broker\\src\\EnterpriseMqttBroker.Core\\Interfaces\\IMqttServerService.cs", "RelativeToolTip": "src\\EnterpriseMqttBroker.Core\\Interfaces\\IMqttServerService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T02:44:04.192Z", "EditorCaption": ""}]}]}]}