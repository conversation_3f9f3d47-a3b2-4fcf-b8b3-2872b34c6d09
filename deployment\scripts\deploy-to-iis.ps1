# EnterpriseMqttBroker IIS部署脚本
# 使用方法: .\deploy-to-iis.ps1 -SiteName "EnterpriseMqttBroker" -AppPoolName "EnterpriseMqttBrokerPool" -PhysicalPath "C:\inetpub\wwwroot\EnterpriseMqttBroker"

param(
    [Parameter(Mandatory=$true)]
    [string]$SiteName,
    
    [Parameter(Mandatory=$true)]
    [string]$AppPoolName,
    
    [Parameter(Mandatory=$true)]
    [string]$PhysicalPath,
    
    [Parameter(Mandatory=$false)]
    [int]$Port = 80,
    
    [Parameter(Mandatory=$false)]
    [int]$HttpsPort = 443,
    
    [Parameter(Mandatory=$false)]
    [string]$BuildConfiguration = "Release"
)

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "此脚本需要管理员权限运行。请以管理员身份运行PowerShell。"
    exit 1
}

# 导入WebAdministration模块
Import-Module WebAdministration -ErrorAction Stop

Write-Host "开始部署 EnterpriseMqttBroker 到 IIS..." -ForegroundColor Green

try {
    # 1. 构建项目
    Write-Host "1. 构建项目..." -ForegroundColor Yellow
    $projectPath = "..\..\src\EnterpriseMqttBroker.WebApi\EnterpriseMqttBroker.WebApi.csproj"
    $publishPath = "..\..\publish"
    
    dotnet publish $projectPath -c $BuildConfiguration -o $publishPath --self-contained false --runtime win-x64
    
    if ($LASTEXITCODE -ne 0) {
        throw "项目构建失败"
    }
    
    # 2. 停止现有应用程序池（如果存在）
    Write-Host "2. 检查并停止现有应用程序池..." -ForegroundColor Yellow
    if (Get-IISAppPool -Name $AppPoolName -ErrorAction SilentlyContinue) {
        Stop-WebAppPool -Name $AppPoolName
        Write-Host "已停止应用程序池: $AppPoolName" -ForegroundColor Green
    }
    
    # 3. 创建物理路径
    Write-Host "3. 创建部署目录..." -ForegroundColor Yellow
    if (!(Test-Path $PhysicalPath)) {
        New-Item -ItemType Directory -Path $PhysicalPath -Force
        Write-Host "已创建目录: $PhysicalPath" -ForegroundColor Green
    }
    
    # 4. 复制文件
    Write-Host "4. 复制应用程序文件..." -ForegroundColor Yellow
    Copy-Item -Path "$publishPath\*" -Destination $PhysicalPath -Recurse -Force
    
    # 复制web.config
    Copy-Item -Path "..\iis\web.config" -Destination $PhysicalPath -Force
    
    # 5. 创建应用程序池
    Write-Host "5. 配置应用程序池..." -ForegroundColor Yellow
    if (Get-IISAppPool -Name $AppPoolName -ErrorAction SilentlyContinue) {
        Remove-WebAppPool -Name $AppPoolName
    }
    
    New-WebAppPool -Name $AppPoolName
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "managedRuntimeVersion" -Value ""
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.idleTimeout" -Value "00:00:00"
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "recycling.periodicRestart.time" -Value "00:00:00"
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.maxProcesses" -Value 1
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "enable32BitAppOnWin64" -Value $false
    
    Write-Host "已配置应用程序池: $AppPoolName" -ForegroundColor Green
    
    # 6. 创建网站
    Write-Host "6. 配置网站..." -ForegroundColor Yellow
    if (Get-Website -Name $SiteName -ErrorAction SilentlyContinue) {
        Remove-Website -Name $SiteName
    }
    
    New-Website -Name $SiteName -PhysicalPath $PhysicalPath -Port $Port -ApplicationPool $AppPoolName
    
    # 添加HTTPS绑定（如果需要）
    if ($HttpsPort -ne 0) {
        New-WebBinding -Name $SiteName -Protocol "https" -Port $HttpsPort
    }
    
    Write-Host "已配置网站: $SiteName" -ForegroundColor Green
    
    # 7. 设置权限
    Write-Host "7. 设置文件权限..." -ForegroundColor Yellow
    $acl = Get-Acl $PhysicalPath
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule)
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IUSR", "ReadAndExecute", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule)
    Set-Acl -Path $PhysicalPath -AclObject $acl
    
    # 8. 启动应用程序池和网站
    Write-Host "8. 启动服务..." -ForegroundColor Yellow
    Start-WebAppPool -Name $AppPoolName
    Start-Website -Name $SiteName
    
    Write-Host "部署完成！" -ForegroundColor Green
    Write-Host "网站地址: http://localhost:$Port" -ForegroundColor Cyan
    if ($HttpsPort -ne 0) {
        Write-Host "HTTPS地址: https://localhost:$HttpsPort" -ForegroundColor Cyan
    }
    
    # 9. 健康检查
    Write-Host "9. 执行健康检查..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$Port/health" -UseBasicParsing -TimeoutSec 30
        if ($response.StatusCode -eq 200) {
            Write-Host "健康检查通过！" -ForegroundColor Green
        } else {
            Write-Warning "健康检查返回状态码: $($response.StatusCode)"
        }
    } catch {
        Write-Warning "健康检查失败: $($_.Exception.Message)"
        Write-Host "请检查应用程序日志以获取更多信息。" -ForegroundColor Yellow
    }
    
} catch {
    Write-Error "部署失败: $($_.Exception.Message)"
    exit 1
}

Write-Host "部署脚本执行完成。" -ForegroundColor Green
