using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using EnterpriseMqttBroker.Core.Interfaces;
using EnterpriseMqttBroker.Infrastructure.Data;
using EnterpriseMqttBroker.Infrastructure.Data.Repositories;

namespace EnterpriseMqttBroker.Infrastructure.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加基础设施服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // 添加数据库上下文
        services.AddDbContext<MqttBrokerDbContext>(options =>
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            options.UseSqlServer(connectionString, sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(30),
                    errorNumbersToAdd: null);
                
                sqlOptions.CommandTimeout(30);
                sqlOptions.MigrationsAssembly("EnterpriseMqttBroker.Infrastructure");
            });

            // 开发环境启用敏感数据日志
            if (configuration.GetValue<bool>("Logging:EnableSensitiveDataLogging"))
            {
                options.EnableSensitiveDataLogging();
            }

            // 开发环境启用详细错误
            if (configuration.GetValue<bool>("Logging:EnableDetailedErrors"))
            {
                options.EnableDetailedErrors();
            }
        });

        // 添加仓储服务
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<IMessageRepository, MessageRepository>();

        // 添加通用仓储
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

        return services;
    }

    /// <summary>
    /// 添加Redis缓存服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddRedisCache(this IServiceCollection services, IConfiguration configuration)
    {
        var redisConnectionString = configuration.GetConnectionString("Redis");
        
        if (!string.IsNullOrEmpty(redisConnectionString))
        {
            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = redisConnectionString;
                options.InstanceName = "EnterpriseMqttBroker";
            });
        }
        else
        {
            // 如果没有配置Redis，使用内存缓存作为后备
            services.AddMemoryCache();
        }

        return services;
    }

    /// <summary>
    /// 添加健康检查
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddCustomHealthChecks(this IServiceCollection services, IConfiguration configuration)
    {
        var healthChecksBuilder = services.AddHealthChecks();

        // 数据库健康检查
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        if (!string.IsNullOrEmpty(connectionString))
        {
            healthChecksBuilder.AddSqlServer(
                connectionString,
                tags: new[] { "database", "sql", "sqlserver" });
        }

        // Redis健康检查
        var redisConnectionString = configuration.GetConnectionString("Redis");
        if (!string.IsNullOrEmpty(redisConnectionString))
        {
            healthChecksBuilder.AddRedis(
                redisConnectionString,
                tags: new[] { "cache", "redis" });
        }

        return services;
    }

    /// <summary>
    /// 确保数据库已创建
    /// </summary>
    /// <param name="services">服务提供者</param>
    /// <returns>异步任务</returns>
    public static async Task EnsureDatabaseCreatedAsync(this IServiceProvider services)
    {
        using var scope = services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<MqttBrokerDbContext>();
        
        try
        {
            // 确保数据库已创建
            await context.Database.EnsureCreatedAsync();
            
            // 或者使用迁移（推荐）
            // await context.Database.MigrateAsync();
        }
        catch (Exception ex)
        {
            // 记录错误但不抛出，让应用程序继续启动
            var logger = scope.ServiceProvider.GetService<Microsoft.Extensions.Logging.ILogger<MqttBrokerDbContext>>();
            logger?.LogError(ex, "Failed to ensure database is created");
        }
    }

    /// <summary>
    /// 运行数据库迁移
    /// </summary>
    /// <param name="services">服务提供者</param>
    /// <returns>异步任务</returns>
    public static async Task MigrateDatabaseAsync(this IServiceProvider services)
    {
        using var scope = services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<MqttBrokerDbContext>();
        
        try
        {
            await context.Database.MigrateAsync();
        }
        catch (Exception ex)
        {
            var logger = scope.ServiceProvider.GetService<Microsoft.Extensions.Logging.ILogger<MqttBrokerDbContext>>();
            logger?.LogError(ex, "Failed to migrate database");
            throw;
        }
    }
}
