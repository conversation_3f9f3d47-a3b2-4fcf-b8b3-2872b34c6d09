using Microsoft.EntityFrameworkCore.Storage;
using EnterpriseMqttBroker.Core.Interfaces;
using EnterpriseMqttBroker.Core.Models;
using EnterpriseMqttBroker.Infrastructure.Data.Repositories;

namespace EnterpriseMqttBroker.Infrastructure.Data;

/// <summary>
/// 工作单元实现
/// </summary>
public class UnitOfWork : IUnitOfWork
{
    /// <summary>
    /// 数据库上下文
    /// </summary>
    private readonly MqttBrokerDbContext _context;

    /// <summary>
    /// 用户仓储
    /// </summary>
    private IRepository<User>? _users;

    /// <summary>
    /// 角色仓储
    /// </summary>
    private IRepository<Role>? _roles;

    /// <summary>
    /// 用户角色仓储
    /// </summary>
    private IRepository<UserRole>? _userRoles;

    /// <summary>
    /// 主题权限仓储
    /// </summary>
    private IRepository<TopicPermission>? _topicPermissions;

    /// <summary>
    /// 持久化消息仓储
    /// </summary>
    private IRepository<PersistedMessage>? _persistedMessages;

    /// <summary>
    /// 是否已释放资源
    /// </summary>
    private bool _disposed = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="context">数据库上下文</param>
    public UnitOfWork(MqttBrokerDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <summary>
    /// 用户仓储
    /// </summary>
    public IRepository<User> Users
    {
        get
        {
            _users ??= new UserRepository(_context);
            return _users;
        }
    }

    /// <summary>
    /// 角色仓储
    /// </summary>
    public IRepository<Role> Roles
    {
        get
        {
            _roles ??= new Repository<Role>(_context);
            return _roles;
        }
    }

    /// <summary>
    /// 用户角色仓储
    /// </summary>
    public IRepository<UserRole> UserRoles
    {
        get
        {
            _userRoles ??= new Repository<UserRole>(_context);
            return _userRoles;
        }
    }

    /// <summary>
    /// 主题权限仓储
    /// </summary>
    public IRepository<TopicPermission> TopicPermissions
    {
        get
        {
            _topicPermissions ??= new Repository<TopicPermission>(_context);
            return _topicPermissions;
        }
    }

    /// <summary>
    /// 持久化消息仓储
    /// </summary>
    public IRepository<PersistedMessage> PersistedMessages
    {
        get
        {
            _persistedMessages ??= new MessageRepository(_context);
            return _persistedMessages;
        }
    }

    /// <summary>
    /// 保存所有更改
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>受影响的行数</returns>
    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// 开始事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>事务对象</returns>
    public async Task<IDbTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        return new DbTransactionWrapper(transaction);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <param name="disposing">是否正在释放</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _context.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// 数据库事务包装器
/// </summary>
internal class DbTransactionWrapper : IDbTransaction
{
    /// <summary>
    /// EF Core 事务
    /// </summary>
    private readonly IDbContextTransaction _transaction;

    /// <summary>
    /// 是否已释放资源
    /// </summary>
    private bool _disposed = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="transaction">EF Core 事务</param>
    public DbTransactionWrapper(IDbContextTransaction transaction)
    {
        _transaction = transaction ?? throw new ArgumentNullException(nameof(transaction));
    }

    /// <summary>
    /// 提交事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task CommitAsync(CancellationToken cancellationToken = default)
    {
        await _transaction.CommitAsync(cancellationToken);
    }

    /// <summary>
    /// 回滚事务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        await _transaction.RollbackAsync(cancellationToken);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <param name="disposing">是否正在释放</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _transaction.Dispose();
            _disposed = true;
        }
    }
}
