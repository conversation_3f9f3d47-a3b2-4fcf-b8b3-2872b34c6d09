using Microsoft.EntityFrameworkCore;
using EnterpriseMqttBroker.Core.Interfaces;
using EnterpriseMqttBroker.Core.Models;

namespace EnterpriseMqttBroker.Infrastructure.Data.Repositories;

/// <summary>
/// 用户仓储实现
/// </summary>
public class UserRepository : Repository<User>, IUserRepository
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="context">数据库上下文</param>
    public UserRepository(MqttBrokerDbContext context) : base(context)
    {
    }

    /// <summary>
    /// 根据用户名获取用户（包含角色信息）
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    public async Task<User?> GetByUsernameWithRolesAsync(string username, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                    .ThenInclude(r => r.TopicPermissions)
            .FirstOrDefaultAsync(u => u.Username == username, cancellationToken);
    }

    /// <summary>
    /// 根据ID获取用户（包含角色信息）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    public async Task<User?> GetByIdWithRolesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                    .ThenInclude(r => r.TopicPermissions)
            .FirstOrDefaultAsync(u => u.Id == userId, cancellationToken);
    }

    /// <summary>
    /// 根据邮箱获取用户
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    public async Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(u => u.Email == email, cancellationToken);
    }

    /// <summary>
    /// 检查用户名是否已存在
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="excludeUserId">排除的用户ID（用于更新时检查）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsUsernameExistsAsync(string username, Guid? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(u => u.Username == username);
        
        if (excludeUserId.HasValue)
        {
            query = query.Where(u => u.Id != excludeUserId.Value);
        }

        return await query.AnyAsync(cancellationToken);
    }

    /// <summary>
    /// 检查邮箱是否已存在
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="excludeUserId">排除的用户ID（用于更新时检查）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsEmailExistsAsync(string email, Guid? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(u => u.Email == email);
        
        if (excludeUserId.HasValue)
        {
            query = query.Where(u => u.Id != excludeUserId.Value);
        }

        return await query.AnyAsync(cancellationToken);
    }

    /// <summary>
    /// 获取活跃用户列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>活跃用户列表</returns>
    public async Task<IEnumerable<User>> GetActiveUsersAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(u => u.IsActive)
            .OrderBy(u => u.Username)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// 获取被锁定的用户列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>被锁定的用户列表</returns>
    public async Task<IEnumerable<User>> GetLockedUsersAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;
        return await _dbSet
            .Where(u => u.LockoutEndTime.HasValue && u.LockoutEndTime.Value > now)
            .OrderBy(u => u.LockoutEndTime)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// 获取用户的权限列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限列表</returns>
    public async Task<IEnumerable<TopicPermission>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _context.TopicPermissions
            .Where(tp => tp.Role.UserRoles.Any(ur => ur.UserId == userId))
            .Include(tp => tp.Role)
            .ToListAsync(cancellationToken);
    }

    /// <summary>
    /// 批量更新用户最后登录时间
    /// </summary>
    /// <param name="userIds">用户ID列表</param>
    /// <param name="loginTime">登录时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task UpdateLastLoginBatchAsync(IEnumerable<Guid> userIds, DateTime loginTime, CancellationToken cancellationToken = default)
    {
        try
        {
            await _dbSet
                .Where(u => userIds.Contains(u.Id))
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(u => u.LastLoginAt, loginTime)
                    .SetProperty(u => u.FailedLoginAttempts, 0),
                    cancellationToken);
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("ExecuteUpdate"))
        {
            // 对于不支持ExecuteUpdate的数据库提供者（如InMemory），使用传统方法
            var users = await _dbSet
                .Where(u => userIds.Contains(u.Id))
                .ToListAsync(cancellationToken);

            foreach (var user in users)
            {
                user.LastLoginAt = loginTime;
                user.FailedLoginAttempts = 0;
            }

            await _context.SaveChangesAsync(cancellationToken);
        }
    }

    /// <summary>
    /// 清理过期的锁定用户
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的用户数量</returns>
    public async Task<int> ClearExpiredLockoutsAsync(CancellationToken cancellationToken = default)
    {
        var now = DateTime.UtcNow;

        try
        {
            return await _dbSet
                .Where(u => u.LockoutEndTime.HasValue && u.LockoutEndTime.Value <= now)
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(u => u.LockoutEndTime, (DateTime?)null)
                    .SetProperty(u => u.FailedLoginAttempts, 0),
                    cancellationToken);
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("ExecuteUpdate"))
        {
            // 对于不支持ExecuteUpdate的数据库提供者（如InMemory），使用传统方法
            var expiredUsers = await _dbSet
                .Where(u => u.LockoutEndTime.HasValue && u.LockoutEndTime.Value <= now)
                .ToListAsync(cancellationToken);

            foreach (var user in expiredUsers)
            {
                user.LockoutEndTime = null;
                user.FailedLoginAttempts = 0;
            }

            await _context.SaveChangesAsync(cancellationToken);
            return expiredUsers.Count;
        }
    }
}
