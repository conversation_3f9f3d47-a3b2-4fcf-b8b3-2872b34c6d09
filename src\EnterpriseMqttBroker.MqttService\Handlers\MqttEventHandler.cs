using Microsoft.Extensions.Logging;
using MQTTnet.Server;
using MQTTnet.Protocol;
using EnterpriseMqttBroker.MqttService.Interfaces;

namespace EnterpriseMqttBroker.MqttService.Handlers;

/// <summary>
/// MQTT事件处理器
/// </summary>
public class MqttEventHandler
{
    private readonly ILogger<MqttEventHandler> _logger;
    private readonly IConnectionManager _connectionManager;
    private readonly IMessageRouter _messageRouter;
    private readonly IAuthenticationHandler _authHandler;
    private readonly IAuthorizationHandler _authzHandler;

    public MqttEventHandler(
        ILogger<MqttEventHandler> logger,
        IConnectionManager connectionManager,
        IMessageRouter messageRouter,
        IAuthenticationHandler authHandler,
        IAuthorizationHandler authzHandler)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        _messageRouter = messageRouter ?? throw new ArgumentNullException(nameof(messageRouter));
        _authHandler = authHandler ?? throw new ArgumentNullException(nameof(authHandler));
        _authzHandler = authzHandler ?? throw new ArgumentNullException(nameof(authzHandler));
    }

    /// <summary>
    /// 处理客户端连接验证
    /// </summary>
    public async Task OnValidatingConnectionAsync(MqttConnectionValidatorContext context)
    {
        try
        {
            var clientId = context.ClientId;
            var endpoint = context.Endpoint ?? "Unknown";

            _logger.LogInformation("客户端连接验证: {ClientId} from {Endpoint}", clientId, endpoint);

            // 检查连接限制
            if (!await _connectionManager.CheckConnectionLimitsAsync(endpoint))
            {
                context.ReasonCode = MqttConnectReasonCode.ServerUnavailable;
                context.ReasonString = "连接数已达上限或IP被限制";
                _logger.LogWarning("连接被拒绝: {ClientId}, 原因: 连接限制", clientId);
                return;
            }

            // 检查客户端是否在黑名单中
            if (await _connectionManager.IsBlacklistedAsync(clientId))
            {
                context.ReasonCode = MqttConnectReasonCode.Banned;
                context.ReasonString = "客户端已被禁止";
                _logger.LogWarning("连接被拒绝: {ClientId}, 原因: 黑名单", clientId);
                return;
            }

            // 执行认证
            var authResult = await _authHandler.ValidateConnectionAsync(context);
            if (!authResult.IsSuccess)
            {
                context.ReasonCode = MqttConnectReasonCode.BadUserNameOrPassword;
                context.ReasonString = authResult.FailureReason ?? "认证失败";
                
                await _authHandler.RecordLoginFailureAsync(
                    context.Username, clientId, endpoint, authResult.FailureReason ?? "认证失败");
                
                _logger.LogWarning("连接被拒绝: {ClientId}, 原因: {Reason}", clientId, authResult.FailureReason);
                return;
            }

            // 认证成功，记录登录
            await _authHandler.RecordLoginSuccessAsync(context.Username, clientId, endpoint);

            // 添加连接到管理器
            await _connectionManager.AddConnectionAsync(clientId, context);

            context.ReasonCode = MqttConnectReasonCode.Success;
            _logger.LogInformation("客户端连接成功: {ClientId}, 用户: {Username}", clientId, context.Username);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理客户端连接验证失败: {ClientId}", context.ClientId);
            context.ReasonCode = MqttConnectReasonCode.ServerUnavailable;
            context.ReasonString = "服务器内部错误";
        }
    }

    /// <summary>
    /// 处理客户端连接事件
    /// </summary>
    public async Task OnClientConnectedAsync(MqttServerClientConnectedEventArgs eventArgs)
    {
        try
        {
            var clientId = eventArgs.ClientId;
            _logger.LogInformation("客户端已连接: {ClientId}", clientId);

            // 更新连接活动时间
            await _connectionManager.UpdateLastActivityAsync(clientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理客户端连接事件失败: {ClientId}", eventArgs.ClientId);
        }
    }

    /// <summary>
    /// 处理客户端断开连接事件
    /// </summary>
    public async Task OnClientDisconnectedAsync(MqttServerClientDisconnectedEventArgs eventArgs)
    {
        try
        {
            var clientId = eventArgs.ClientId;
            _logger.LogInformation("客户端已断开: {ClientId}, 类型: {Type}", clientId, eventArgs.DisconnectType);

            // 移除客户端连接
            await _connectionManager.RemoveConnectionAsync(clientId);

            // 如果不是Clean Session，保留订阅
            if (eventArgs.DisconnectType != MqttClientDisconnectType.Clean)
            {
                _logger.LogDebug("保留客户端订阅: {ClientId}", clientId);
            }
            else
            {
                // Clean Session，移除所有订阅
                await _messageRouter.RemoveAllSubscriptionsAsync(clientId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理客户端断开连接事件失败: {ClientId}", eventArgs.ClientId);
        }
    }

    /// <summary>
    /// 处理应用消息拦截
    /// </summary>
    public async Task OnInterceptingApplicationMessageAsync(MqttApplicationMessageInterceptorContext context)
    {
        try
        {
            var clientId = context.ClientId;
            var topic = context.ApplicationMessage.Topic;

            _logger.LogDebug("拦截应用消息: 客户端={ClientId}, 主题={Topic}", clientId, topic);

            // 更新客户端活动时间
            await _connectionManager.UpdateLastActivityAsync(clientId);

            // 检查发布权限
            var authResult = await _authzHandler.ValidatePublishAsync(context);
            if (!authResult.IsAuthorized)
            {
                context.AcceptPublish = false;
                context.CloseConnection = false; // 不断开连接，只拒绝消息
                
                await _authzHandler.RecordAuthorizationFailureAsync(
                    context.SessionItems.ContainsKey("Username") ? context.SessionItems["Username"]?.ToString() : null,
                    clientId, "Publish", topic, authResult.Reason ?? "权限不足");

                _logger.LogWarning("发布被拒绝: 客户端={ClientId}, 主题={Topic}, 原因={Reason}", 
                    clientId, topic, authResult.Reason);
                return;
            }

            // 如果有修改的主题，应用修改
            if (!string.IsNullOrEmpty(authResult.ModifiedTopic))
            {
                context.ApplicationMessage.Topic = authResult.ModifiedTopic;
                _logger.LogDebug("主题已修改: {OriginalTopic} -> {ModifiedTopic}", topic, authResult.ModifiedTopic);
            }

            // 如果有修改的QoS，应用修改
            if (authResult.ModifiedQoS.HasValue)
            {
                context.ApplicationMessage.QualityOfServiceLevel = (MQTTnet.Protocol.MqttQualityOfServiceLevel)authResult.ModifiedQoS.Value;
            }

            context.AcceptPublish = true;

            // 路由消息到订阅者
            await _messageRouter.RouteMessageAsync(context.ApplicationMessage, clientId);

            // 更新客户端统计
            await _connectionManager.UpdateClientStatisticsAsync(
                clientId, 1, 0, context.ApplicationMessage.PayloadSegment.Count, 0);

            _logger.LogDebug("消息发布成功: 客户端={ClientId}, 主题={Topic}", clientId, context.ApplicationMessage.Topic);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理应用消息拦截失败: 客户端={ClientId}, 主题={Topic}", 
                context.ClientId, context.ApplicationMessage.Topic);
            context.AcceptPublish = false;
        }
    }

    /// <summary>
    /// 处理订阅拦截
    /// </summary>
    public async Task OnInterceptingSubscriptionAsync(MqttSubscriptionInterceptorContext context)
    {
        try
        {
            var clientId = context.ClientId;
            var topicFilter = context.TopicFilter.Topic;

            _logger.LogDebug("拦截订阅: 客户端={ClientId}, 主题过滤器={TopicFilter}", clientId, topicFilter);

            // 更新客户端活动时间
            await _connectionManager.UpdateLastActivityAsync(clientId);

            // 检查订阅权限
            var authResult = await _authzHandler.ValidateSubscribeAsync(context);
            if (!authResult.IsAuthorized)
            {
                context.AcceptSubscription = false;
                context.CloseConnection = false;

                await _authzHandler.RecordAuthorizationFailureAsync(
                    context.SessionItems.ContainsKey("Username") ? context.SessionItems["Username"]?.ToString() : null,
                    clientId, "Subscribe", topicFilter, authResult.Reason ?? "权限不足");

                _logger.LogWarning("订阅被拒绝: 客户端={ClientId}, 主题过滤器={TopicFilter}, 原因={Reason}", 
                    clientId, topicFilter, authResult.Reason);
                return;
            }

            context.AcceptSubscription = true;

            // 添加订阅到路由器
            await _messageRouter.AddSubscriptionAsync(clientId, topicFilter, (byte)context.TopicFilter.QualityOfServiceLevel);

            _logger.LogDebug("订阅成功: 客户端={ClientId}, 主题过滤器={TopicFilter}", clientId, topicFilter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理订阅拦截失败: 客户端={ClientId}, 主题过滤器={TopicFilter}", 
                context.ClientId, context.TopicFilter.Topic);
            context.AcceptSubscription = false;
        }
    }

    /// <summary>
    /// 处理取消订阅拦截
    /// </summary>
    public async Task OnInterceptingUnsubscriptionAsync(MqttUnsubscriptionInterceptorContext context)
    {
        try
        {
            var clientId = context.ClientId;
            var topicFilter = context.Topic;

            _logger.LogDebug("拦截取消订阅: 客户端={ClientId}, 主题过滤器={TopicFilter}", clientId, topicFilter);

            // 更新客户端活动时间
            await _connectionManager.UpdateLastActivityAsync(clientId);

            // 检查取消订阅权限（通常都允许）
            var authResult = await _authzHandler.ValidateUnsubscribeAsync(context);
            if (!authResult.IsAuthorized)
            {
                context.AcceptUnsubscription = false;
                _logger.LogWarning("取消订阅被拒绝: 客户端={ClientId}, 主题过滤器={TopicFilter}, 原因={Reason}", 
                    clientId, topicFilter, authResult.Reason);
                return;
            }

            context.AcceptUnsubscription = true;

            // 从路由器移除订阅
            await _messageRouter.RemoveSubscriptionAsync(clientId, topicFilter);

            _logger.LogDebug("取消订阅成功: 客户端={ClientId}, 主题过滤器={TopicFilter}", clientId, topicFilter);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理取消订阅拦截失败: 客户端={ClientId}, 主题过滤器={TopicFilter}", 
                context.ClientId, context.Topic);
            context.AcceptUnsubscription = false;
        }
    }
}
