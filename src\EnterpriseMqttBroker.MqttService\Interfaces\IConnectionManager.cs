using MQTTnet.Server;
using EnterpriseMqttBroker.Core.Interfaces;

namespace EnterpriseMqttBroker.MqttService.Interfaces;

/// <summary>
/// 连接管理器接口
/// </summary>
public interface IConnectionManager
{
    /// <summary>
    /// 添加客户端连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="context">连接上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task AddConnectionAsync(string clientId, MqttConnectionValidatorContext context, CancellationToken cancellationToken = default);

    /// <summary>
    /// 移除客户端连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task RemoveConnectionAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端连接信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>连接信息</returns>
    Task<MqttClientStatus?> GetConnectionAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有连接的客户端
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>客户端状态列表</returns>
    Task<IEnumerable<MqttClientStatus>> GetAllConnectionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查客户端是否已连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>是否已连接</returns>
    bool IsConnected(string clientId);

    /// <summary>
    /// 获取连接数量
    /// </summary>
    /// <returns>连接数量</returns>
    int GetConnectionCount();

    /// <summary>
    /// 更新客户端活动时间
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task UpdateLastActivityAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    Task CleanupExpiredConnectionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端统计信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    Task<ClientStatistics?> GetClientStatisticsAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 更新客户端统计信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="messagesSent">发送消息数</param>
    /// <param name="messagesReceived">接收消息数</param>
    /// <param name="bytesSent">发送字节数</param>
    /// <param name="bytesReceived">接收字节数</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task UpdateClientStatisticsAsync(string clientId, long messagesSent, long messagesReceived, 
        long bytesSent, long bytesReceived, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查连接限制
    /// </summary>
    /// <param name="endpoint">客户端终结点</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否允许连接</returns>
    Task<bool> CheckConnectionLimitsAsync(string endpoint, CancellationToken cancellationToken = default);

    /// <summary>
    /// 添加客户端到黑名单
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="reason">黑名单原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task BlacklistClientAsync(string clientId, string reason, CancellationToken cancellationToken = default);

    /// <summary>
    /// 从黑名单移除客户端
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task RemoveFromBlacklistAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查客户端是否在黑名单中
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否在黑名单中</returns>
    Task<bool> IsBlacklistedAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取连接管理器性能指标
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>性能指标</returns>
    Task<ConnectionManagerMetrics> GetMetricsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 客户端统计信息
/// </summary>
public class ClientStatistics
{
    public string ClientId { get; set; } = string.Empty;
    public long MessagesSent { get; set; }
    public long MessagesReceived { get; set; }
    public long BytesSent { get; set; }
    public long BytesReceived { get; set; }
    public DateTime ConnectedAt { get; set; }
    public DateTime LastActivity { get; set; }
    public int SubscriptionsCount { get; set; }
    public string ProtocolVersion { get; set; } = string.Empty;
    public string Endpoint { get; set; } = string.Empty;
    public bool IsCleanSession { get; set; }
    public TimeSpan SessionDuration => DateTime.UtcNow - ConnectedAt;
}

/// <summary>
/// 连接管理器性能指标
/// </summary>
public class ConnectionManagerMetrics
{
    public int TotalConnections { get; set; }
    public int MaxConnections { get; set; }
    public double ConnectionUtilization { get; set; }
    public int BlacklistedClients { get; set; }
    public TimeSpan AverageSessionDuration { get; set; }
    public long TotalBytesTransferred { get; set; }
    public long TotalMessagesTransferred { get; set; }
    public Dictionary<string, int> ConnectionsPerProtocol { get; set; } = new();
    public List<ClientActivityInfo> TopClientsByActivity { get; set; } = new();
}

/// <summary>
/// 客户端活动信息
/// </summary>
public class ClientActivityInfo
{
    public string ClientId { get; set; } = string.Empty;
    public long TotalMessages { get; set; }
    public long TotalBytes { get; set; }
    public TimeSpan SessionDuration { get; set; }
}
