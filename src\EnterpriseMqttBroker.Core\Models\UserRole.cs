namespace EnterpriseMqttBroker.Core.Models;

/// <summary>
/// 用户角色关联实体
/// </summary>
public class UserRole
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// 角色ID
    /// </summary>
    public Guid RoleId { get; set; }

    /// <summary>
    /// 分配时间
    /// </summary>
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 用户导航属性
    /// </summary>
    public virtual User User { get; set; } = null!;

    /// <summary>
    /// 角色导航属性
    /// </summary>
    public virtual Role Role { get; set; } = null!;
}
