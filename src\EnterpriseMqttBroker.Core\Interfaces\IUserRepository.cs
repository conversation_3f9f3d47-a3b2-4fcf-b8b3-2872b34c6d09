using EnterpriseMqttBroker.Core.Models;

namespace EnterpriseMqttBroker.Core.Interfaces;

/// <summary>
/// 用户仓储接口
/// </summary>
public interface IUserRepository : IRepository<User>
{
    /// <summary>
    /// 根据用户名获取用户（包含角色信息）
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    Task<User?> GetByUsernameWithRolesAsync(string username, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据ID获取用户（包含角色信息）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    Task<User?> GetByIdWithRolesAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 根据邮箱获取用户
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>用户实体</returns>
    Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查用户名是否已存在
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="excludeUserId">排除的用户ID（用于更新时检查）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    Task<bool> IsUsernameExistsAsync(string username, Guid? excludeUserId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查邮箱是否已存在
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <param name="excludeUserId">排除的用户ID（用于更新时检查）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    Task<bool> IsEmailExistsAsync(string email, Guid? excludeUserId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取活跃用户列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>活跃用户列表</returns>
    Task<IEnumerable<User>> GetActiveUsersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取被锁定的用户列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>被锁定的用户列表</returns>
    Task<IEnumerable<User>> GetLockedUsersAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户的权限列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>权限列表</returns>
    Task<IEnumerable<TopicPermission>> GetUserPermissionsAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量更新用户最后登录时间
    /// </summary>
    /// <param name="userIds">用户ID列表</param>
    /// <param name="loginTime">登录时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task UpdateLastLoginBatchAsync(IEnumerable<Guid> userIds, DateTime loginTime, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理过期的锁定用户
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理的用户数量</returns>
    Task<int> ClearExpiredLockoutsAsync(CancellationToken cancellationToken = default);
}
