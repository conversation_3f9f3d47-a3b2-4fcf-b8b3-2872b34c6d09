{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=EnterpriseMqttBrokerDb;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true", "Redis": "localhost:6379"}, "MqttSettings": {"TcpPort": 1883, "WebSocketPort": 8083, "TlsPort": 8883, "MaxPendingMessagesPerClient": 250, "DefaultCommunicationTimeout": "00:01:00", "MaxConnections": 10000, "EnablePersistentSessions": true, "EnableRetainedMessages": true, "EnableWillMessages": true, "ClientIdRequired": true, "AllowAnonymousConnections": false}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "EnterpriseMqttBroker", "Audience": "EnterpriseMqttBrokerUsers", "ExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "SecuritySettings": {"RequireHttps": false, "EnableCertificateAuthentication": false, "MaxFailedLoginAttempts": 5, "LockoutDurationMinutes": 15, "PasswordRequirements": {"MinLength": 8, "RequireUppercase": true, "RequireLowercase": true, "RequireDigit": true, "RequireSpecialCharacter": true}}, "MonitoringSettings": {"EnableMetricsCollection": true, "MetricsCollectionIntervalSeconds": 30, "EnableHealthChecks": true, "HealthCheckIntervalSeconds": 60, "EnablePerformanceCounters": true}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/mqtt-broker-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}