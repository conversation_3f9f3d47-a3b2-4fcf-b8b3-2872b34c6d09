using Microsoft.EntityFrameworkCore;
using EnterpriseMqttBroker.Core.Models;
using EnterpriseMqttBroker.Infrastructure.Data;
using EnterpriseMqttBroker.Infrastructure.Data.Repositories;
using Xunit;
using FluentAssertions;

namespace EnterpriseMqttBroker.UnitTests.Infrastructure;

/// <summary>
/// 用户仓储测试
/// </summary>
public class UserRepositoryTests : IDisposable
{
    private readonly MqttBrokerDbContext _context;
    private readonly UserRepository _userRepository;

    public UserRepositoryTests()
    {
        // 使用内存数据库进行测试
        var options = new DbContextOptionsBuilder<MqttBrokerDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new MqttBrokerDbContext(options);
        _userRepository = new UserRepository(_context);

        // 不使用EnsureCreated以避免种子数据，InMemory数据库会自动创建表结构
    }

    [Fact]
    public async Task GetByUsernameWithRolesAsync_ShouldReturnUserWithRoles_WhenUserExists()
    {
        // Arrange
        var role = new Role
        {
            Id = Guid.NewGuid(),
            Name = "TestRole",
            Description = "Test Role",
            CreatedAt = DateTime.UtcNow
        };

        var user = new User
        {
            Id = Guid.NewGuid(),
            Username = "testuser",
            PasswordHash = "hashedpassword",
            Email = "<EMAIL>",
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        var userRole = new UserRole
        {
            UserId = user.Id,
            RoleId = role.Id,
            AssignedAt = DateTime.UtcNow
        };

        _context.Roles.Add(role);
        _context.Users.Add(user);
        _context.UserRoles.Add(userRole);
        await _context.SaveChangesAsync();

        // Act
        var result = await _userRepository.GetByUsernameWithRolesAsync("testuser");

        // Assert
        result.Should().NotBeNull();
        result!.Username.Should().Be("testuser");
        result.UserRoles.Should().HaveCount(1);
        result.UserRoles.First().Role.Name.Should().Be("TestRole");
    }

    [Fact]
    public async Task GetByUsernameWithRolesAsync_ShouldReturnNull_WhenUserDoesNotExist()
    {
        // Act
        var result = await _userRepository.GetByUsernameWithRolesAsync("nonexistentuser");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task IsUsernameExistsAsync_ShouldReturnTrue_WhenUsernameExists()
    {
        // Arrange
        var user = new User
        {
            Id = Guid.NewGuid(),
            Username = "existinguser",
            PasswordHash = "hashedpassword",
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        // Act
        var result = await _userRepository.IsUsernameExistsAsync("existinguser");

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsUsernameExistsAsync_ShouldReturnFalse_WhenUsernameDoesNotExist()
    {
        // Act
        var result = await _userRepository.IsUsernameExistsAsync("nonexistentuser");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task GetActiveUsersAsync_ShouldReturnOnlyActiveUsers()
    {
        // Arrange
        var activeUser = new User
        {
            Id = Guid.NewGuid(),
            Username = "activeuser",
            PasswordHash = "hashedpassword",
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        var inactiveUser = new User
        {
            Id = Guid.NewGuid(),
            Username = "inactiveuser",
            PasswordHash = "hashedpassword",
            IsActive = false,
            CreatedAt = DateTime.UtcNow
        };

        _context.Users.AddRange(activeUser, inactiveUser);
        await _context.SaveChangesAsync();

        // Act
        var result = await _userRepository.GetActiveUsersAsync();

        // Assert
        result.Should().HaveCount(1);
        result.First().Username.Should().Be("activeuser");
    }

    [Fact]
    public async Task GetLockedUsersAsync_ShouldReturnOnlyLockedUsers()
    {
        // Arrange
        var lockedUser = new User
        {
            Id = Guid.NewGuid(),
            Username = "lockeduser",
            PasswordHash = "hashedpassword",
            IsActive = true,
            LockoutEndTime = DateTime.UtcNow.AddHours(1), // 锁定1小时
            CreatedAt = DateTime.UtcNow
        };

        var normalUser = new User
        {
            Id = Guid.NewGuid(),
            Username = "normaluser",
            PasswordHash = "hashedpassword",
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        _context.Users.AddRange(lockedUser, normalUser);
        await _context.SaveChangesAsync();

        // Act
        var result = await _userRepository.GetLockedUsersAsync();

        // Assert
        result.Should().HaveCount(1);
        result.First().Username.Should().Be("lockeduser");
    }

    [Fact]
    public async Task ClearExpiredLockoutsAsync_ShouldClearExpiredLockouts()
    {
        // Arrange
        var expiredLockoutUser = new User
        {
            Id = Guid.NewGuid(),
            Username = "expireduser",
            PasswordHash = "hashedpassword",
            IsActive = true,
            LockoutEndTime = DateTime.UtcNow.AddHours(-1), // 过期锁定
            FailedLoginAttempts = 5,
            CreatedAt = DateTime.UtcNow
        };

        var activeLockoutUser = new User
        {
            Id = Guid.NewGuid(),
            Username = "activelockeduser",
            PasswordHash = "hashedpassword",
            IsActive = true,
            LockoutEndTime = DateTime.UtcNow.AddHours(1), // 未过期锁定
            FailedLoginAttempts = 3,
            CreatedAt = DateTime.UtcNow
        };

        _context.Users.AddRange(expiredLockoutUser, activeLockoutUser);
        await _context.SaveChangesAsync();

        // Act
        var clearedCount = await _userRepository.ClearExpiredLockoutsAsync();

        // Assert
        clearedCount.Should().Be(1);

        // 验证过期锁定已清除
        var updatedExpiredUser = await _context.Users.FindAsync(expiredLockoutUser.Id);
        updatedExpiredUser!.LockoutEndTime.Should().BeNull();
        updatedExpiredUser.FailedLoginAttempts.Should().Be(0);

        // 验证未过期锁定未被清除
        var updatedActiveUser = await _context.Users.FindAsync(activeLockoutUser.Id);
        updatedActiveUser!.LockoutEndTime.Should().NotBeNull();
        updatedActiveUser.FailedLoginAttempts.Should().Be(3);
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
