using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EnterpriseMqttBroker.Core.Models;

namespace EnterpriseMqttBroker.Infrastructure.Data.Configurations;

/// <summary>
/// 持久化消息实体配置
/// </summary>
public class PersistedMessageConfiguration : IEntityTypeConfiguration<PersistedMessage>
{
    /// <summary>
    /// 配置持久化消息实体
    /// </summary>
    /// <param name="builder">实体类型构建器</param>
    public void Configure(EntityTypeBuilder<PersistedMessage> builder)
    {
        // 表名
        builder.ToTable("PersistedMessages");

        // 主键
        builder.HasKey(pm => pm.Id);
        builder.Property(pm => pm.Id)
            .ValueGeneratedOnAdd()
            .HasComment("消息ID");

        // 客户端ID
        builder.Property(pm => pm.ClientId)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("客户端ID");

        // 主题
        builder.Property(pm => pm.Topic)
            .IsRequired()
            .HasMaxLength(255)
            .HasComment("消息主题");

        // 消息载荷
        builder.Property(pm => pm.Payload)
            .HasColumnType("varbinary(max)")
            .HasComment("消息载荷");

        // QoS级别
        builder.Property(pm => pm.QoSLevel)
            .IsRequired()
            .HasColumnType("tinyint")
            .HasComment("QoS级别");

        // 保留标志
        builder.Property(pm => pm.Retain)
            .IsRequired()
            .HasDefaultValue(false)
            .HasComment("是否为保留消息");

        // 创建时间
        builder.Property(pm => pm.CreatedAt)
            .IsRequired()
            .HasColumnType("datetime2")
            .HasDefaultValueSql("GETUTCDATE()")
            .HasComment("创建时间");

        // 过期间隔（秒）
        builder.Property(pm => pm.ExpiryInterval)
            .HasComment("消息过期间隔（秒）");

        // 索引优化
        builder.HasIndex(pm => pm.ClientId)
            .HasDatabaseName("IX_PersistedMessages_ClientId");

        builder.HasIndex(pm => pm.Topic)
            .HasDatabaseName("IX_PersistedMessages_Topic");

        builder.HasIndex(pm => pm.CreatedAt)
            .HasDatabaseName("IX_PersistedMessages_CreatedAt");

        builder.HasIndex(pm => pm.QoSLevel)
            .HasDatabaseName("IX_PersistedMessages_QoSLevel");

        // 保留消息专用索引
        builder.HasIndex(pm => new { pm.Topic, pm.Retain })
            .HasFilter("[Retain] = 1")
            .HasDatabaseName("IX_PersistedMessages_Topic_Retain");

        // 客户端消息查询优化索引
        builder.HasIndex(pm => new { pm.ClientId, pm.CreatedAt })
            .HasDatabaseName("IX_PersistedMessages_ClientId_CreatedAt");

        // 主题和时间复合索引
        builder.HasIndex(pm => new { pm.Topic, pm.CreatedAt })
            .HasDatabaseName("IX_PersistedMessages_Topic_CreatedAt");

        // 过期消息清理索引
        builder.HasIndex(pm => new { pm.ExpiryInterval, pm.CreatedAt })
            .HasFilter("[ExpiryInterval] IS NOT NULL")
            .HasDatabaseName("IX_PersistedMessages_ExpiryInterval_CreatedAt");

        // QoS和时间复合索引
        builder.HasIndex(pm => new { pm.QoSLevel, pm.CreatedAt })
            .HasDatabaseName("IX_PersistedMessages_QoSLevel_CreatedAt");
    }
}
