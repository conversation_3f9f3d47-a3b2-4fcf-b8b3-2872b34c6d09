using MQTTnet.Server;

namespace EnterpriseMqttBroker.MqttService.Interfaces;

/// <summary>
/// MQTT授权处理器接口
/// </summary>
public interface IAuthorizationHandler
{
    /// <summary>
    /// 验证发布权限
    /// </summary>
    /// <param name="context">应用消息拦截上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>授权结果</returns>
    Task<AuthorizationResult> ValidatePublishAsync(MqttApplicationMessageInterceptorContext context, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证订阅权限
    /// </summary>
    /// <param name="context">订阅拦截上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>授权结果</returns>
    Task<AuthorizationResult> ValidateSubscribeAsync(MqttSubscriptionInterceptorContext context, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证取消订阅权限
    /// </summary>
    /// <param name="context">取消订阅拦截上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>授权结果</returns>
    Task<AuthorizationResult> ValidateUnsubscribeAsync(MqttUnsubscriptionInterceptorContext context, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查用户对主题的发布权限
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topic">主题</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否有权限</returns>
    Task<bool> CanPublishAsync(string? username, string clientId, string topic, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查用户对主题的订阅权限
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否有权限</returns>
    Task<bool> CanSubscribeAsync(string? username, string clientId, string topicFilter, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取用户的主题权限列表
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>主题权限列表</returns>
    Task<IEnumerable<TopicPermissionInfo>> GetUserTopicPermissionsAsync(string username, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取角色的主题权限列表
    /// </summary>
    /// <param name="roleName">角色名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>主题权限列表</returns>
    Task<IEnumerable<TopicPermissionInfo>> GetRoleTopicPermissionsAsync(string roleName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查主题是否匹配权限模式
    /// </summary>
    /// <param name="topic">主题</param>
    /// <param name="permissionPattern">权限模式</param>
    /// <returns>是否匹配</returns>
    bool IsTopicMatchPermissionPattern(string topic, string permissionPattern);

    /// <summary>
    /// 记录授权失败事件
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="action">操作类型</param>
    /// <param name="topic">主题</param>
    /// <param name="reason">失败原因</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task RecordAuthorizationFailureAsync(string? username, string clientId, string action, string topic, string reason, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取授权统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>授权统计信息</returns>
    Task<AuthorizationStatistics> GetAuthorizationStatisticsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 授权结果
/// </summary>
public class AuthorizationResult
{
    public bool IsAuthorized { get; set; }
    public string? Reason { get; set; }
    public string? ModifiedTopic { get; set; }
    public byte? ModifiedQoS { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();

    public static AuthorizationResult Allow()
    {
        return new AuthorizationResult { IsAuthorized = true };
    }

    public static AuthorizationResult Allow(string modifiedTopic)
    {
        return new AuthorizationResult 
        { 
            IsAuthorized = true, 
            ModifiedTopic = modifiedTopic 
        };
    }

    public static AuthorizationResult Deny(string reason)
    {
        return new AuthorizationResult 
        { 
            IsAuthorized = false, 
            Reason = reason 
        };
    }
}

/// <summary>
/// 主题权限信息
/// </summary>
public class TopicPermissionInfo
{
    public string TopicPattern { get; set; } = string.Empty;
    public bool CanPublish { get; set; }
    public bool CanSubscribe { get; set; }
    public string? RoleName { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// 授权统计信息
/// </summary>
public class AuthorizationStatistics
{
    public long TotalAuthorizationChecks { get; set; }
    public long SuccessfulAuthorizations { get; set; }
    public long FailedAuthorizations { get; set; }
    public long PublishChecks { get; set; }
    public long SubscribeChecks { get; set; }
    public double SuccessRate => TotalAuthorizationChecks > 0 ? 
        (double)SuccessfulAuthorizations / TotalAuthorizationChecks * 100 : 0;
    public Dictionary<string, long> FailureReasons { get; set; } = new();
    public Dictionary<string, long> TopicAccessCounts { get; set; } = new();
    public DateTime LastResetTime { get; set; }
}
