<?xml version="1.0"?>
<doc>
    <assembly>
        <name>EnterpriseMqttBroker.WebApi</name>
    </assembly>
    <members>
        <member name="T:EnterpriseMqttBroker.WebApi.Program">
            <summary>
            应用程序入口点
            </summary>
        </member>
        <member name="M:EnterpriseMqttBroker.WebApi.Program.Main(System.String[])">
            <summary>
            主入口方法
            </summary>
            <param name="args">命令行参数</param>
        </member>
    </members>
</doc>
