using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Caching.Distributed;
using MQTTnet.Server;
using EnterpriseMqttBroker.Core.Interfaces;
using EnterpriseMqttBroker.MqttService.Configuration;
using EnterpriseMqttBroker.MqttService.Interfaces;
using System.Collections.Concurrent;
using System.Text.Json;
using System.Net;
using CoreMqttClientStatus = EnterpriseMqttBroker.Core.Interfaces.MqttClientStatus;
using MqttNetMqttClientStatus = MQTTnet.Server.MqttClientStatus;

namespace EnterpriseMqttBroker.MqttService.Managers;

/// <summary>
/// 连接管理器实现
/// </summary>
public class ConnectionManager : IConnectionManager
{
    private readonly ILogger<ConnectionManager> _logger;
    private readonly Configuration.MqttServerOptions _options;
    private readonly IDistributedCache _cache;
    
    private readonly ConcurrentDictionary<string, CoreMqttClientStatus> _connections;
    private readonly ConcurrentDictionary<string, ClientStatistics> _statistics;
    private readonly ConcurrentDictionary<string, DateTime> _connectionAttempts;
    private readonly ConcurrentDictionary<string, string> _blacklist;
    
    private readonly Timer _cleanupTimer;
    private readonly object _lockObject = new();

    public ConnectionManager(
        ILogger<ConnectionManager> logger,
        IOptions<Configuration.MqttServerOptions> options,
        IDistributedCache cache)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        
        _connections = new ConcurrentDictionary<string, CoreMqttClientStatus>();
        _statistics = new ConcurrentDictionary<string, ClientStatistics>();
        _connectionAttempts = new ConcurrentDictionary<string, DateTime>();
        _blacklist = new ConcurrentDictionary<string, string>();
        
        // 启动清理定时器，每分钟执行一次
        _cleanupTimer = new Timer(async _ => await CleanupExpiredConnectionsAsync(), 
            null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    /// <summary>
    /// 添加客户端连接
    /// </summary>
    public async Task AddConnectionAsync(string clientId, MqttConnectionValidatorContext context, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            throw new ArgumentException("客户端ID不能为空", nameof(clientId));

        try
        {
            var now = DateTime.UtcNow;
            var endpoint = context.Endpoint ?? "Unknown";

            // 创建客户端状态
            var clientStatus = new CoreMqttClientStatus
            {
                ClientId = clientId,
                Endpoint = endpoint,
                ConnectedAt = now,
                LastActivity = now,
                ProtocolVersion = context.ProtocolVersion?.ToString() ?? "Unknown"
            };

            // 创建统计信息
            var statistics = new ClientStatistics
            {
                ClientId = clientId,
                ConnectedAt = now,
                LastActivity = now,
                Endpoint = endpoint,
                ProtocolVersion = context.ProtocolVersion?.ToString() ?? "Unknown",
                IsCleanSession = context.CleanSession
            };

            // 添加到内存缓存
            _connections.TryAdd(clientId, clientStatus);
            _statistics.TryAdd(clientId, statistics);

            // 持久化到分布式缓存
            await SaveConnectionToCacheAsync(clientId, clientStatus, cancellationToken);
            await SaveStatisticsToCacheAsync(clientId, statistics, cancellationToken);

            _logger.LogInformation("客户端连接已添加: {ClientId} from {Endpoint}", clientId, endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加客户端连接失败: {ClientId}", clientId);
            throw;
        }
    }

    /// <summary>
    /// 移除客户端连接
    /// </summary>
    public async Task RemoveConnectionAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            return;

        try
        {
            // 从内存缓存移除
            _connections.TryRemove(clientId, out var connection);
            _statistics.TryRemove(clientId, out var statistics);

            // 从分布式缓存移除
            await _cache.RemoveAsync($"connection:{clientId}", cancellationToken);
            await _cache.RemoveAsync($"statistics:{clientId}", cancellationToken);

            _logger.LogInformation("客户端连接已移除: {ClientId}", clientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "移除客户端连接失败: {ClientId}", clientId);
        }
    }

    /// <summary>
    /// 获取客户端连接信息
    /// </summary>
    public async Task<CoreMqttClientStatus?> GetConnectionAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            return null;

        // 先从内存缓存获取
        if (_connections.TryGetValue(clientId, out var connection))
            return connection;

        // 从分布式缓存获取
        try
        {
            var cached = await _cache.GetStringAsync($"connection:{clientId}", cancellationToken);
            if (!string.IsNullOrEmpty(cached))
            {
                var status = JsonSerializer.Deserialize<CoreMqttClientStatus>(cached);
                if (status != null)
                {
                    _connections.TryAdd(clientId, status);
                    return status;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从缓存获取连接信息失败: {ClientId}", clientId);
        }

        return null;
    }

    /// <summary>
    /// 获取所有连接的客户端
    /// </summary>
    public async Task<IEnumerable<CoreMqttClientStatus>> GetAllConnectionsAsync(CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(_connections.Values.ToList());
    }

    /// <summary>
    /// 检查客户端是否已连接
    /// </summary>
    public bool IsConnected(string clientId)
    {
        return !string.IsNullOrEmpty(clientId) && _connections.ContainsKey(clientId);
    }

    /// <summary>
    /// 获取连接数量
    /// </summary>
    public int GetConnectionCount()
    {
        return _connections.Count;
    }

    /// <summary>
    /// 更新客户端活动时间
    /// </summary>
    public async Task UpdateLastActivityAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            return;

        try
        {
            var now = DateTime.UtcNow;

            // 更新内存缓存
            if (_connections.TryGetValue(clientId, out var connection))
            {
                connection.LastActivity = now;
            }

            if (_statistics.TryGetValue(clientId, out var statistics))
            {
                statistics.LastActivity = now;
                await SaveStatisticsToCacheAsync(clientId, statistics, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新客户端活动时间失败: {ClientId}", clientId);
        }
    }

    /// <summary>
    /// 清理过期连接
    /// </summary>
    public async Task CleanupExpiredConnectionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var now = DateTime.UtcNow;
            var timeout = TimeSpan.FromSeconds(_options.ConnectionTimeoutSeconds);
            var expiredClients = new List<string>();

            // 查找过期连接
            foreach (var kvp in _connections)
            {
                if (now - kvp.Value.LastActivity > timeout)
                {
                    expiredClients.Add(kvp.Key);
                }
            }

            // 移除过期连接
            foreach (var clientId in expiredClients)
            {
                await RemoveConnectionAsync(clientId, cancellationToken);
                _logger.LogInformation("已清理过期连接: {ClientId}", clientId);
            }

            // 清理连接尝试记录
            var expiredAttempts = _connectionAttempts
                .Where(kvp => now - kvp.Value > TimeSpan.FromMinutes(5))
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var endpoint in expiredAttempts)
            {
                _connectionAttempts.TryRemove(endpoint, out _);
            }

            if (expiredClients.Count > 0 || expiredAttempts.Count > 0)
            {
                _logger.LogInformation("清理完成: 过期连接 {ExpiredConnections} 个, 过期尝试记录 {ExpiredAttempts} 个", 
                    expiredClients.Count, expiredAttempts.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理过期连接失败");
        }
    }

    /// <summary>
    /// 获取客户端统计信息
    /// </summary>
    public async Task<ClientStatistics?> GetClientStatisticsAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            return null;

        // 先从内存缓存获取
        if (_statistics.TryGetValue(clientId, out var statistics))
            return statistics;

        // 从分布式缓存获取
        try
        {
            var cached = await _cache.GetStringAsync($"statistics:{clientId}", cancellationToken);
            if (!string.IsNullOrEmpty(cached))
            {
                var stats = JsonSerializer.Deserialize<ClientStatistics>(cached);
                if (stats != null)
                {
                    _statistics.TryAdd(clientId, stats);
                    return stats;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从缓存获取统计信息失败: {ClientId}", clientId);
        }

        return null;
    }

    /// <summary>
    /// 更新客户端统计信息
    /// </summary>
    public async Task UpdateClientStatisticsAsync(string clientId, long messagesSent, long messagesReceived, 
        long bytesSent, long bytesReceived, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            return;

        try
        {
            if (_statistics.TryGetValue(clientId, out var statistics))
            {
                statistics.MessagesSent += messagesSent;
                statistics.MessagesReceived += messagesReceived;
                statistics.BytesSent += bytesSent;
                statistics.BytesReceived += bytesReceived;
                statistics.LastActivity = DateTime.UtcNow;

                await SaveStatisticsToCacheAsync(clientId, statistics, cancellationToken);
            }

            // 同时更新连接状态
            if (_connections.TryGetValue(clientId, out var connection))
            {
                connection.BytesSent = bytesSent;
                connection.BytesReceived = bytesReceived;
                connection.LastActivity = DateTime.UtcNow;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新客户端统计信息失败: {ClientId}", clientId);
        }
    }

    /// <summary>
    /// 检查连接限制
    /// </summary>
    public async Task<bool> CheckConnectionLimitsAsync(string endpoint, CancellationToken cancellationToken = default)
    {
        try
        {
            // 检查最大连接数
            if (GetConnectionCount() >= _options.MaxConnections)
            {
                _logger.LogWarning("已达到最大连接数限制: {MaxConnections}", _options.MaxConnections);
                return false;
            }

            // 检查IP黑名单
            if (_options.Security.IpBlacklist.Any(ip => endpoint.Contains(ip)))
            {
                _logger.LogWarning("IP在黑名单中: {Endpoint}", endpoint);
                return false;
            }

            // 检查IP白名单
            if (_options.Security.IpWhitelist.Count > 0 && 
                !_options.Security.IpWhitelist.Any(ip => endpoint.Contains(ip)))
            {
                _logger.LogWarning("IP不在白名单中: {Endpoint}", endpoint);
                return false;
            }

            // 检查速率限制
            if (_options.Security.EnableRateLimiting)
            {
                var now = DateTime.UtcNow;
                var windowStart = now.AddSeconds(-_options.Security.RateLimit.WindowSizeSeconds);
                
                var recentAttempts = _connectionAttempts.Count(kvp => 
                    kvp.Key.Contains(endpoint) && kvp.Value > windowStart);

                if (recentAttempts >= _options.Security.RateLimit.MaxConnectionsPerSecond)
                {
                    _logger.LogWarning("连接速率限制: {Endpoint}, 尝试次数: {Attempts}", endpoint, recentAttempts);
                    return false;
                }

                // 记录连接尝试
                _connectionAttempts.TryAdd($"{endpoint}:{now.Ticks}", now);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查连接限制失败: {Endpoint}", endpoint);
            return false;
        }
    }

    /// <summary>
    /// 添加客户端到黑名单
    /// </summary>
    public async Task BlacklistClientAsync(string clientId, string reason, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            return;

        try
        {
            _blacklist.TryAdd(clientId, reason);
            await _cache.SetStringAsync($"blacklist:{clientId}", reason, cancellationToken);
            
            _logger.LogWarning("客户端已加入黑名单: {ClientId}, 原因: {Reason}", clientId, reason);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加黑名单失败: {ClientId}", clientId);
        }
    }

    /// <summary>
    /// 从黑名单移除客户端
    /// </summary>
    public async Task RemoveFromBlacklistAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            return;

        try
        {
            _blacklist.TryRemove(clientId, out _);
            await _cache.RemoveAsync($"blacklist:{clientId}", cancellationToken);
            
            _logger.LogInformation("客户端已从黑名单移除: {ClientId}", clientId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从黑名单移除失败: {ClientId}", clientId);
        }
    }

    /// <summary>
    /// 检查客户端是否在黑名单中
    /// </summary>
    public async Task<bool> IsBlacklistedAsync(string clientId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(clientId))
            return false;

        // 先检查内存缓存
        if (_blacklist.ContainsKey(clientId))
            return true;

        // 检查分布式缓存
        try
        {
            var cached = await _cache.GetStringAsync($"blacklist:{clientId}", cancellationToken);
            if (!string.IsNullOrEmpty(cached))
            {
                _blacklist.TryAdd(clientId, cached);
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查黑名单失败: {ClientId}", clientId);
        }

        return false;
    }

    /// <summary>
    /// 保存连接信息到缓存
    /// </summary>
    private async Task SaveConnectionToCacheAsync(string clientId, CoreMqttClientStatus status, CancellationToken cancellationToken)
    {
        try
        {
            var json = JsonSerializer.Serialize(status);
            var options = new DistributedCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromHours(1)
            };
            await _cache.SetStringAsync($"connection:{clientId}", json, options, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存连接信息到缓存失败: {ClientId}", clientId);
        }
    }

    /// <summary>
    /// 保存统计信息到缓存
    /// </summary>
    private async Task SaveStatisticsToCacheAsync(string clientId, ClientStatistics statistics, CancellationToken cancellationToken)
    {
        try
        {
            var json = JsonSerializer.Serialize(statistics);
            var options = new DistributedCacheEntryOptions
            {
                SlidingExpiration = TimeSpan.FromHours(1)
            };
            await _cache.SetStringAsync($"statistics:{clientId}", json, options, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存统计信息到缓存失败: {ClientId}", clientId);
        }
    }

    public void Dispose()
    {
        _cleanupTimer?.Dispose();
    }
}
